import { Competency } from 'src/resources/individual-develop-plans/competencies/entities/competencies.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  Column,
} from 'typeorm';
import { Skill } from './skill.entity';

// competency-skill.entity.ts
@Entity('IDP_T_COMPETENCY_SKILLS', { comment: 'ตารางความสัมพันธ์สมรรถนะกับทักษะ' })
export class CompetencySkill {
  @PrimaryGeneratedColumn({ name: 'CPS_ID', comment: 'รหัสความสัมพันธ์สมรรถนะกับทักษะ' })
  id: number;

  @ManyToOne(() => Competency, (c) => c.competencySkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'CPT_ID' })
  competency: Competency;

  @ManyToOne(() => Skill, (s) => s.competencySkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'SK_ID' })
  skill: Skill;
}
