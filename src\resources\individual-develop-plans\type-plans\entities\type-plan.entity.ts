import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON>inColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { DevelopmentPlan } from '../../development-plans/entities/development-plan.entity';
import { AgeWork } from '../../age-work/entities/age-work.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { Position } from 'src/resources/positions/entities/position.entity';
import { Career } from '../../careers/entities/careers.entity';
import { Skill } from '../../skills/entities/skill.entity';

export enum PlanType {
  GENERAL_STAFF = 'ทั่วไปบุคลากร',
  GENERAL_MANAGER = 'ทั่วไปผู้บริหาร',
  SPECIFIC_MANAGEMENT = 'เฉพาะด้านบริหาร',
  SPECIFIC_ACADEMIC = 'เฉพาะด้านวิชาการ',
  SPECIFIC_SUPPORT = 'เฉพาะสายสนับสนุน',
  POSITION = 'ตำแหน่ง',
}

@Entity('IDP_T_TYPE_PLANS', {
  comment: 'แผนย่อยของแผนพัฒนาบุคลากร (Development Plan)',
})
export class TypePlan {
  @PrimaryGeneratedColumn({ name: 'TP_ID', comment: 'รหัสแผนย่อย' })
  id: number;

  @Column({ name: 'DP_ID', nullable: false, comment: 'รหัสแผนพัฒนาบุคลากร' })
  developmentPlanId: number;

  @Column({ name: 'TP_PRIVATE_USER_ID', nullable: true, comment: 'รหัสผู้ใช้ส่วนตัว' })
  privateUserId: string | null;

  @Column({
    name: 'TP_NAME',
    type: 'enum',
    enum: PlanType,
    enumName: 'PLAN_TYPE_ENUM', // กำหนดชื่อ enum ใน database
    nullable: true,
    comment: 'ประเภทแผนย',
  })
  name: PlanType | null;

  @Column({ name: 'AGE_WORK_ID', nullable: true , comment: 'รหัส Age Work' })
  ageWorkId: number | null;

  @Column({ name: 'POSITION_ID', nullable: true, comment: 'รหัสตำแหน่ง' })
  positionId: number | null;

  @Column({ name: 'CAREER_ID', nullable: true, comment: 'รหัสสายอาชีพ' })
  careerId: number | null;

  @ManyToOne(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.typePlans,
    { nullable: false },
  )
  @JoinColumn({ name: 'DP_ID' })
  developmentPlan: DevelopmentPlan;

  // age work entity not age work criteria
  @ManyToOne(() => AgeWork, (ageWork) => ageWork.typePlans)
  @JoinColumn({ name: 'AGE_WORK_ID' })
  ageWork: AgeWork;

  @ManyToOne(() => User, (user) => user.privatePlans, { nullable: true })
  @JoinColumn({ name: 'PRIVATE_USER_ID' })
  privatePlanUser: User;

  @ManyToOne(() => Position, (position) => position.typePlans, {
    nullable: true,
  })
  @JoinColumn({ name: 'POSITION_ID' })
  position: Position;

  @ManyToOne(() => Career, (career) => career.typePlans, { nullable: true })
  @JoinColumn({ name: 'CAREER_ID' })
  career: Career;

  // many to many skill
  @ManyToMany(() => Skill, (skill) => skill.typePlans, { cascade: true })
  @JoinTable({
    name: 'IDP_T_TYPE_PLAN_SKILLS',
    joinColumn: { name: 'TP_ID' },
    inverseJoinColumn: { name: 'SK_ID' },
  })
  skills: Skill[];
}
