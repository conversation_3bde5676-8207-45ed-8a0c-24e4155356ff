import { DataSource, DataSourceOptions } from 'typeorm';
import { runSeeders } from 'typeorm-extension';
import { MainSeeder } from './main.seeder';
import { UserFactory } from './factories/user.factory';
import { PermissionFactory } from './factories/permission.factory';
import { RoleFactory } from './factories/role.factory';
import { getTypeOrmConfig } from '../configs/typeorm.config';

import * as dotenv from 'dotenv';
import * as fs from 'fs';

// Try to load .env.local first, then fallback to .env
if (fs.existsSync('.env.local')) {
  dotenv.config({ path: '.env.local' });
} else if (fs.existsSync('.env')) {
  dotenv.config({ path: '.env' });
} else {
  dotenv.config();
}


// Extend the imported config for seeding
const options: DataSourceOptions & any = {
  ...getTypeOrmConfig(),
  logging: process.env.NODE_ENV === 'development',
  factories: [UserFactory, PermissionFactory, RoleFactory],
  seeds: [MainSeeder],
};

async function runDatabaseSeeding() {
  console.log('🚀 Initializing database connection for seeding...');
  console.log(`🌐 Database: ${JSON.stringify(options, null, 2)}`);

  const dataSource = new DataSource(options);

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established successfully!');
    console.log(`📍 Connected to database: ${options.database}`);

    // Run seeders
    await runSeeders(dataSource);

    console.log('🏁 Database seeding process completed!');
  } catch (error) {
    console.error('💥 Database seeding failed:', error);

    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error(
        '🔐 Access denied. Please check your database credentials.',
      );
    } else if (error.code === 'ECONNREFUSED') {
      console.error(
        '🔌 Connection refused. Please check if your database server is running.',
      );
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error(
        '🗄️ Database does not exist. Please create the database first.',
      );
    }

    process.exit(1);
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('🔒 Database connection closed.');
    }
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

if (require.main === module) {
  runDatabaseSeeding();
}
