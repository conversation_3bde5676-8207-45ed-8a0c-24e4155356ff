import { Role } from '../../roles/entities/role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
} from 'typeorm';

@Entity('HRD_T_PERMISSIONS', {
  comment: 'ตารางสิทธิ์การใช้งาน'
})
export class Permission {
  @PrimaryGeneratedColumn({ name: 'PER_ID', comment: 'รหัสสิทธิ์' })
  id: number;

  @Column({ name: 'PER_NAME', nullable: false, comment: 'ชื่อสิทธิ์' })
  name: string;

  @Column({ name: 'PER_DESC_EN', nullable: false, comment: 'รายละเอียดสิทธิ์ (EN)' })
  descEn: string;

  @Column({ name: 'PER_DESC_TH', nullable: false, comment: 'รายละเอียดสิทธิ์ (TH)' })
  descTh: string;

  @Column({ name: 'PER_STATUS', default: true, comment: 'สถานะการใช้งาน' })
  status: boolean;

  @Column({ name: 'PER_IS_DEFAULT', default: false, comment: 'สิทธิ์เริ่มต้น' })
  isDefault: boolean;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];
}
