import { Faker } from '@faker-js/faker';
import { setSeederFactory } from 'typeorm-extension';
import { User } from '../../resources/users/entities/user.entity';

export const UserFactory = setSeederFactory(User, (faker) => {
  const user = new User();
  user.id = faker.string.alphanumeric(10);
  user.idcard = faker.string.numeric(13);
  user.prefixTh = faker.helpers.arrayElement(['นาย', 'นาง', 'นางสาว']);
  user.prefixEn = faker.helpers.arrayElement(['Mr.', 'Mrs.', 'Miss']);
  user.firstNameTh = faker.person.firstName();
  user.firstNameEn = faker.person.firstName();
  user.lastNameTh = faker.person.lastName();
  user.lastNameEn = faker.person.lastName();
  user.campusName = faker.company.name();
  user.facultyName = faker.company.name();
  user.departmentName = faker.company.name();
  user.groupName = faker.company.name();
  user.positionName = faker.person.jobTitle();
  user.positionLevel = faker.helpers.arrayElement(['ปฏิบัติการ', 'ชำนาญการ', 'ชำนาญการพิเศษ', 'เชี่ยวชาญ']);
  user.startDate = faker.date.past();
  user.workStatus = 'N';
  user.email = faker.internet.email();
  user.username = faker.internet.userName();
  // Note: Don't set password here as it will be hashed by the entity
  // เดี๋ยวมาแก้ให้
  return user;
});
