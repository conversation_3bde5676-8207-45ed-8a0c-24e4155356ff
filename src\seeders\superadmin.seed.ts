import { DataSource } from 'typeorm';
import { User } from '../resources/users/entities/user.entity';
import { Role } from '../resources/roles/entities/role.entity';
import { Permission } from '../resources/permissions/entities/permission.entity';
import * as bcrypt from 'bcrypt';

export async function seedSuperAdmin(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);
  const roleRepository = dataSource.getRepository(Role);
  const permissionRepository = dataSource.getRepository(Permission);

  console.log('Starting superadmin seeding...');

  // Get superadmin credentials from environment variables
  const superAdminEmail = process.env.SUPERADMIN_EMAIL || 'superadmin';
  const superAdminPassword = process.env.SUPERADMIN_PASSWORD || '1234';

  // Check if superadmin already exists
  const existingSuperAdmin = await userRepository.findOne({
    where: { email: superAdminEmail }
  });

  if (existingSuperAdmin) {
    console.log(`SuperAdmin already exists with email: ${superAdminEmail}`);
    return;
  }

  // Create super_operator permission
  let superOperatorPermission = await permissionRepository.findOne({
    where: { name: 'super_operator' }
  });

  if (!superOperatorPermission) {
    superOperatorPermission = permissionRepository.create({
      name: 'super_operator',
      descEn: 'Super Operator',
      descTh: 'ผู้ดูแลระบบ',
      status: true,
      isDefault: false
    });
    await permissionRepository.save(superOperatorPermission);
    console.log('Created super_operator permission');
  }

  // Create superadmin role
  let superAdminRole = await roleRepository.findOne({
    where: { name: 'ผู้ดูแลระบบศสูงสุด' },
    relations: ['permissions']
  });

  if (!superAdminRole) {
    superAdminRole = roleRepository.create({
      name: 'ผู้ดูแลระบบศสูงสุด',
      description: 'มีสิทธิ์เข้าถึงและจัดการทุกฟังก์ชันในระบบ รวมถึงการกำหนดสิทธิ์ การตั้งค่าระบบหลัก และการบริหารผู้ใช้งานทุกระดับ',
      department: 'กบกพ.',
    });
    await roleRepository.save(superAdminRole);
    console.log('Created ผู้ดูแลระบบศสูงสุด role');
  }

  // Assign super_operator permission to superadmin role if not already assigned
  const hasPermission = superAdminRole.permissions?.some(p => p.id === superOperatorPermission.id);
  if (!hasPermission) {
    await dataSource.query(`
      INSERT IGNORE INTO HRD_T_ROLES_HAS_PERMISSIONS (ROL_ID, PER_ID) 
      VALUES (?, ?)
    `, [superAdminRole.id, superOperatorPermission.id]);
    console.log('Assigned super_operator permission to superadmin role');
  }

  // Create superadmin user
  const hashedPassword = await bcrypt.hash(superAdminPassword, 10);
  
  const superAdminUser = userRepository.create({
    id: '6800001',
    idcard: '1212312121000',
    prefixTh: 'นาย',
    prefixEn: 'Mr.',
    firstNameTh: 'ฟ้าครื้ม',
    firstNameEn: 'Fahkruem',
    lastNameTh: 'สว่างจัง',
    lastNameEn: 'Sawangjang',
    email: superAdminEmail,
    password: hashedPassword,
    username: 'superadmin',
    campusName: 'บางแสน',
    facultyName: 'คณะวิทยาการสารสนเทศ',
    departmentName: 'สำนักงานคณบดี',
    groupName: 'วิชาการ',
    positionName: 'ผู้ช่วยศาสตราจารย์',
    positionLevel: 'ชำนาญการพิเศษ',
    startDate: new Date(),
    workStatus: 'N',
  });

  await userRepository.save(superAdminUser);
  console.log(`Created superadmin user with email: ${superAdminEmail}`);

  // Assign user to department role
  await dataSource.query(`
    INSERT IGNORE INTO HRD_T_FAC_DEP_USER_ROLES (PSN_ID, ROL_ID, HRD_IS_DEP_DIRECTOR , DEP_ID) 
    VALUES (?, ?, ?, ?)
  `, [superAdminUser.id, superAdminRole.id, 1, '02']);
  console.log(`Assigned superadmin user to role: ${superAdminRole.name}`);

  console.log('SuperAdmin seeding completed successfully!');
  console.log('Login credentials:');
  console.log(`Email: ${superAdminEmail}`);
  console.log('Password: [From SUPERADMIN_PASSWORD environment variable]');
}
