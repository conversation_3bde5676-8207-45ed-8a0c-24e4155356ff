import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Repository, FindManyOptions } from 'typeorm';
import { Role } from './entities/role.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { Permission } from '../permissions/entities/permission.entity';
import { User } from '../users/entities/user.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { Faculty } from '../faculties/entities/faculty.entity';
import { FacDepUserRoles } from '../faculties/entities/faculty-deparment-user-role.entity';

export interface CreateRoleResponse {
  data: {
    id: number;
    name: string;
    description: string;
    permissions: Permission[];
  };
  message: string;
  statusCode: number;
}

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Faculty)
    private readonly facultyRepository: Repository<Faculty>,
    @InjectRepository(FacDepUserRoles)
    private readonly facDepUserRolesRepository: Repository<FacDepUserRoles>,
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<CreateRoleResponse> {
    const { name, description, permissionIds } = createRoleDto;

    if (!description || description.trim() === '') {
      throw new ConflictException('Description must not be empty or null.');
    }

    const existingRole = await this.roleRepository.findOne({ where: { name } });
    if (existingRole) {
      throw new ConflictException(`Role with name "${name}" already exists.`);
    }

    const newRole = this.roleRepository.create({ name, description });

    // ดึง default permissions
    const defaultPermissions = await this.permissionRepository.find({
      where: { isDefault: true },
    });

    // ดึง permissions จาก permissionIds ถ้ามี
    let extraPermissions: Permission[] = [];
    if (permissionIds && permissionIds.length > 0) {
      extraPermissions = await this.permissionRepository.find({
        where: { id: In(permissionIds) },
      });

      if (extraPermissions.length !== permissionIds.length) {
        throw new NotFoundException(
          'Some permissions in permissionIds not found.',
        );
      }
    }

    // รวม permissions ทั้งหมดและกำจัดซ้ำ
    const permissionMap = new Map<number, Permission>();
    [...defaultPermissions, ...extraPermissions].forEach((p) =>
      permissionMap.set(p.id, p),
    );
    newRole.permissions = Array.from(permissionMap.values());
    const savedRole = await this.roleRepository.save(newRole);

    return {
      data: {
        id: savedRole.id,
        name: savedRole.name,
        description: savedRole.description,
        permissions: savedRole.permissions,
      },
      message: 'Role created successfully',
      statusCode: 201,
    };
  }

  // async addPermissionsToRole(roleId: number, permissionIds: number[]) {
  //   const role = await this.roleRepository.findOne({
  //     where: { id: roleId },
  //     relations: ['permissions'],
  //   });
  //   if (!role) throw new NotFoundException('Role not found');
  //   const permissions = await this.permissionRepository.find({
  //     where: { id: In(permissionIds) },
  //   });
  //   if (permissions.length === 0) throw new NotFoundException('Permissions not found');
  //   const newPermissions = permissions.filter(
  //     (p) => !role.permissions.some((rp) => rp.id === p.id),
  //   );
  //   role.permissions.push(...newPermissions);
  //   return this.roleRepository.save(role);
  // }

  async findAll(pag?: DataParams): Promise<DataResponse<Role>> {
    const queryOptions: FindManyOptions<Role> = {
      relations: ['permissions'],
      cache: true,
    };

    // ถ้ามี pagination parameters
    if (pag) {
      if (pag.search) {
        queryOptions.where = { name: Like(`%${pag.search}%`) };
      }
      if (pag.order) {
        queryOptions.order = { id: pag.order };
      }
      if (pag.page && pag.limit) {
        queryOptions.skip = (pag.page - 1) * pag.limit;
        queryOptions.take = pag.limit;
      }
    }

    const [roles, total] = await this.roleRepository.findAndCount(queryOptions);

    return {
      data: roles,
      total,
      currentPage: pag?.page || 1,
      itemsPerPage: pag?.limit || 10,
      totalPages: Math.ceil(total / pag.limit),
      hasNext: pag ? total > pag.page * pag.limit : false,
      hasPrev: pag ? pag.page > 1 : false,
    };
  }

  async findOne(id: number): Promise<Role> {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect(
        'role.permissions',
        'permission',
        'permission.status = :status',
        { status: true },
      )
      .where('role.id = :id', { id })
      .getOne();

    if (!role) {
      throw new NotFoundException(`Role with ID "${id}" not found`);
    }
    return role;
  }

  async update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role> {
    const role = await this.findOne(id);
    const { name, description, permissionIds } = updateRoleDto;

    if (name && name !== role.name) {
      const existingRole = await this.roleRepository.findOneBy({ name });
      if (existingRole && existingRole.id !== id) {
        throw new ConflictException(`Role with name "${name}" already exists.`);
      }
      role.name = name;
    }

    if (description === undefined || description.trim() === '') {
      throw new ConflictException('Description must not be empty.');
    }
    role.description = description;

    if (permissionIds !== undefined) {
      const permissions = permissionIds.length
        ? await this.permissionRepository.find({
            where: { id: In(permissionIds) },
          })
        : [];

      if (permissionIds.length !== permissions.length) {
        throw new NotFoundException(
          'Some permissions in permissionIds not found.',
        );
      }

      role.permissions = permissions;
    }

    return this.roleRepository.save(role);
  }

  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    await this.roleRepository.remove(role);
  }

  async updateFacultyUsersRole(
    roleId: number,
    userIds: number[],
    facultyId: number,
  ): Promise<{
    message: string;
    addedUsers: User[];
    removedUsers: User[];
  }> {
    if (userIds.length > 0) {
      const users = await this.userRepository.findBy({ id: In(userIds.map(id => String(id))) });
      if (users.length !== userIds.length) {
        const foundUserIds = users.map((u) => u.id);
        const notFoundUserIds = userIds.filter(
          (id) => !foundUserIds.includes(String(id)),
        );
        throw new NotFoundException(
          `Some users in userIds not found: ${notFoundUserIds.join(', ')}`,
        );
      }
    }

    // Find current user roles for this faculty and role
    const currentUserRoles = await this.facDepUserRolesRepository.find({
      where: { 
        roleId: roleId,
        personId: In(userIds.map(id => String(id))),
        department: {
          faculty: { id: String(facultyId) }
        }
      },
      relations: ['user', 'role', 'department', 'department.faculty'],
    });
    console.log('currentUserRoles', currentUserRoles);

    const currentUserIds = currentUserRoles.map((ur) => Number(ur.personId));
    console.log('currentUserIds', currentUserIds);
    
    const usersToAdd = userIds.filter((id) => !currentUserIds.includes(id));
    console.log('usersToAdd', usersToAdd);
    
    const usersToRemove = currentUserIds.filter((id) => !userIds.includes(id));
    console.log('usersToRemove', usersToRemove);

    // Add new user roles
    if (usersToAdd.length > 0) {
      // Find departments for this faculty to assign users to
      // For simplicity, we'll assign to the first department of the faculty
      // You might want to modify this logic based on your business requirements
      const facultyDepartments = await this.facDepUserRolesRepository
        .createQueryBuilder('facDepUserRoles')
        .leftJoinAndSelect('facDepUserRoles.department', 'department')
        .leftJoinAndSelect('department.faculty', 'faculty')
        .where('faculty.id = :facultyId', { facultyId: String(facultyId) })
        .getMany();

      if (facultyDepartments.length === 0) {
        throw new NotFoundException(`No departments found for faculty ${facultyId}`);
      }

      const defaultDepartment = facultyDepartments[0].department;

      const newUserRoles = usersToAdd.map((userId) =>
        this.facDepUserRolesRepository.create({
          personId: String(userId),
          roleId: roleId,
          departmentId: defaultDepartment.id,
        }),
      );
      await this.facDepUserRolesRepository.save(newUserRoles);
    }

    // Remove user roles
    if (usersToRemove.length > 0) {
      await this.facDepUserRolesRepository.delete({
        roleId: roleId,
        personId: In(usersToRemove.map(id => String(id))),
        department: {
          faculty: { id: String(facultyId) }
        }
      });
    }

    const addedUsers =
      usersToAdd.length > 0
        ? await this.userRepository.findBy({ id: In(usersToAdd.map(id => String(id))) })
        : [];
    const removedUsers =
      usersToRemove.length > 0
        ? await this.userRepository.findBy({ id: In(usersToRemove.map(id => String(id))) })
        : [];

    return {
      message: 'Faculty users roles updated successfully',
      addedUsers,
      removedUsers,
    };
  }

  // async removePermissionsFromRole(roleId: number, permissionIds: number[]) {
  //   const role = await this.roleRepository.findOne({
  //     where: { id: roleId },
  //     relations: ['permissions'],
  //   });
  //   if (!role) throw new NotFoundException('Role not found');
  //   const permissionsToRemove = await this.permissionRepository.find({
  //     where: { id: In(permissionIds) },
  //   });
  //   if (permissionsToRemove.length === 0) throw new NotFoundException('Permissions not found');
  //   role.permissions = role.permissions.filter(
  //     (p) => !permissionIds.includes(p.id),
  //   );

  //   return this.roleRepository.save(role);
  // }
}
