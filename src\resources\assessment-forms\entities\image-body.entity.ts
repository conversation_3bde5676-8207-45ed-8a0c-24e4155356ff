import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('ASM_T_IMAGE_BODIES',{comment: 'ตารางบล็อกส่วนรูปภาพในแบบประเมินผล (Quiz / Evaluate Form)'})
export class ImageBody {
  @PrimaryGeneratedColumn({ name: 'IMB_ID', comment: 'รหัสส่วนรูปภาพ' })
  id: number;

  @Column({ name: 'IMB_IMAGE_TEXT', nullable: true, comment: 'ข้อความประกอบรูปภาพ' })
  imageText: string | null;

  @Column({ name: 'IMB_IMAGE_PATH', nullable: true, type: 'text', comment: 'ที่อยู่รูปภาพ' })
  imagePath: string | null;

  @Column({ name: 'IMB_IMAGE_WIDTH', nullable: true, comment: 'ความกว้างรูปภาพ' })
  imageWidth: number | null;

  @Column({ name: 'IMB_IMAGE_HEIGHT', nullable: true, comment: 'ความสูงรูปภาพ' })
  imageHeight: number | null;

  @Column({ name: 'I<PERSON>_<PERSON>', comment: 'รหัสบล็อก' })
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.imageBody, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;
}
