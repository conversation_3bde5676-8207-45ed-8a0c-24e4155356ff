import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseInterceptors,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { UpdateFacultyUsersRoleDto } from './dto/update-role.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiQuery,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @RequirePermissions('role_create')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreateRoleDto })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  // @Post(':id/permissions')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       permissionIds: {
  //         type: 'array',
  //         items: { type: 'number' },
  //         example: [1, 2, 3],
  //       },
  //     },
  //     required: ['permissionIds'],
  //   },
  // })
  // addPermissionsToRole(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body('permissionIds') permissionIds: number[],
  // ) {
  //   return this.rolesService.addPermissionsToRole(id, permissionIds);
  // }

  @Get()
  @DefaultQueryParams()
  findAll(@Query() query?: DataParams) {
    return this.rolesService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.findOne(id);
  }

  @Patch(':id')
  @RequirePermissions('role_update')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UpdateRoleDto })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateRoleDto: UpdateRoleDto,
  ) {
    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  @RequirePermissions('role_delete')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.remove(id);
  }

  @Patch(':id/facultyUsersRole')
  @RequirePermissions('role_update')
  @ApiOperation({
    summary: 'Update faculty users roles based on checkbox selection',
  })
  @ApiBody({
    type: UpdateFacultyUsersRoleDto,
    examples: {
      updateFacultyUsersRole: {
        value: {
          userIds: [1, 2, 3],
          facultyId: 1,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Faculty users roles successfully updated',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Role or users not found' })
  async updateFacultyUsersRole(
    @Param('id') id: string,
    @Body() updateFacultyUsersRoleDto: UpdateFacultyUsersRoleDto,
  ) {
    return this.rolesService.updateFacultyUsersRole(
      +id,
      updateFacultyUsersRoleDto.userIds,
      updateFacultyUsersRoleDto.facultyId,
    );
  }
}
