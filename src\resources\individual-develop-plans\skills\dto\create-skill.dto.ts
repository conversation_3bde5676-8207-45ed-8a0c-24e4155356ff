// src/skills/dto/create-skill.dto.ts

import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsInt,
  IsArray,
  IsBoolean,
} from 'class-validator';
import { PlanType } from '../../type-plans/entities/type-plan.entity';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSkillDto {
  @ApiProperty({ example: 'Skill name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: 'Skill description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 'ทั่วไปบุคลากร', required: false })
  @IsOptional()
  @IsEnum(PlanType)
  career_type: PlanType | null;

  @ApiProperty({ example: 1 })
  @IsInt()
  programId: number;

  @ApiProperty({ example: true, required: false })
  @IsBoolean()
  tracking: boolean;

  @ApiProperty({ example: 1, required: false })
  @IsOptional()
  @IsString()
  evaluatorId: string; // userId ของผู้ประเมิน

  @ApiProperty({ example: 1, required: false })
  @IsOptional()
  @IsInt()
  dep_id: number; // ส่วนงาน

  @ApiProperty({ required: false, example: [1, 2] })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  competencyIds?: number[];
}
