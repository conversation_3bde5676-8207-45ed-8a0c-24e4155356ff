import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  <PERSON>To<PERSON>ne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { Submission } from './submission.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';

@Entity('ASM_T_RESPONSES', {
  comment: 'ตารางการตอบกลับแบบประเมินผล (Quiz / Evaluate Form)',
})
export class Response {
  @PrimaryGeneratedColumn({ name: 'RES_ID', comment: 'รหัสการตอบกลับ' })
  id: number;

  @Column({ name: 'SBM_ID', nullable: false, comment: 'รหัสการส่งแบบประเมิน' })
  submissionId: number;

  @Column({ name: 'OPT_ID', nullable: true, comment: 'รหัสตัวเลือกที่เลือก' })
  selectedOptionId: number | null;

  @Column({ name: 'QST_ID', comment: 'รหัสคำถาม' })
  questionId: number;

  @ManyToOne(() => Submission, (submission) => submission.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: '<PERSON>BM_<PERSON>' })
  submission: Submission;

  @ManyToOne(() => Option, (option) => option.responses, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'OPT_ID' })
  selectedOption: Option | null;

  @ManyToOne(() => Question, (question) => question.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'QST_ID' })
  question: Question;
}
