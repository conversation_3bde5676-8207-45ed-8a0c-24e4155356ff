import {
  Column,
  <PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from 'src/resources/roles/entities/role.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { Department } from 'src/resources/department/entities/department.entity';

@Entity('HRD_T_FAC_DEP_USER_ROLES', { comment: 'ตารางบทบาทผู้ใช้ในคณะและกลุ่มงาน' })
export class FacDepUserRoles {
  @PrimaryGeneratedColumn({ name: 'FUR_ID', comment: 'รหัสบทบาทผู้ใช้' })
  id: number;

  @Column({ name: 'DEP_ID', nullable: true, comment: 'รหัสกลุ่มงาน', length: 10 })
  departmentId: string;

  @Column({ name: 'ROL_ID', nullable: false, comment: 'รหัสบทบาท' })
  roleId: number;

  @Column({ name: 'PSN_ID', nullable: false, comment: 'รหัสบุคลากร', length: 10 })
  personId: string;

  @Column({ name: 'HRD_IS_DEP_DIRECTOR', nullable: false, comment: 'เป็นหัวหน้ากลุ่มงานหรือไม่', default: false })
  isDepartmentDirector: boolean;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'DEP_ID' })
  department: Department;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'PSN_ID' })
  user: User;

  @ManyToOne(() => Role, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ROL_ID' })
  role: Role;
}
