import { Injectable } from '@nestjs/common';
import { CreateProgramDto } from './dto/create-program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { EntityManager, Like } from 'typeorm';
import { Program } from './entities/program.entity';
import { DataParams, createPaginatedResponse } from 'src/types';

@Injectable()
export class ProgramsService {
  constructor(private readonly entityManager: EntityManager) {}

  async findAll(pag?: DataParams) {
    const repoProgram = this.entityManager.getRepository(Program);
    
    const [programs, total] = await repoProgram.findAndCount({
      where: {
        name: pag?.search ? Like(`%${pag.search}%`) : undefined,
        description: pag?.search ? Like(`%${pag.search}%`) : undefined,
      },
      order: {
        [pag?.sortBy || 'id']: pag?.order || 'ASC',
      },
      skip: (pag?.page - 1) * pag?.limit,
      take: pag?.limit,
    });

    return createPaginatedResponse(programs, total, pag?.page || 1, pag?.limit || 10);
  }

  findOne(id: number) {
    const repoProgram = this.entityManager.getRepository(Program);
    return repoProgram.findOneOrFail({ where: { id } });
  }

  create(createProgramDto: CreateProgramDto) {
    const repoProgram = this.entityManager.getRepository(Program);
    const program = repoProgram.create(createProgramDto);
    return repoProgram.save(program);
  }

  update(id: number, updateProgramDto: UpdateProgramDto) {
    const repoProgram = this.entityManager.getRepository(Program);
    return repoProgram.update(id, updateProgramDto);
  }

  remove(id: number) {
    const repoProgram = this.entityManager.getRepository(Program);
    return repoProgram.delete(id);
  }
}
