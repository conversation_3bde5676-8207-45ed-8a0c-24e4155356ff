/**
 * Main index file for all type definitions
 * Exports all types, interfaces, and utility functions for easy importing
 */

// Export DataTablePaginate and related utilities
export * from './dataTable';

// Export query parameters and related types
export * from './params';

// Export API-related DTOs
export * from './api/uploadFile';

// Re-export commonly used types for convenience
export type {
  DataResponse,
  DataParams,
  DevPlanTabs,
  DevelopmentPlanParams,
} from './params';

export {
  DEFAULT_PAGINATION,
  normalizeQueryParams,
  isValidDevPlanTab,
} from './params';

export { DataTablePaginate } from './dataTable';

export { UploadFileDto, UploadFileResponseDto } from './api/uploadFile';