import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AgeWorkService } from './age-work.service';
import { CreateAgeWorkDto, UpdateAgeWorkDto } from './dto/age-work.dto';
import type { DataParams, DataResponse } from 'src/types/params';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { AgeWork } from './entities/age-work.entity';

@ApiBearerAuth()
@ApiTags('Age Work')
@Controller('age-works')
export class AgeWorkController {
  constructor(private readonly ageWorkService: AgeWorkService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new age work' })
  @ApiBody({ type: CreateAgeWorkDto })
  @ApiResponse({ status: 201, description: 'Age work successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createDto: CreateAgeWorkDto) {
    return this.ageWorkService.createAgeWork(createDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an age work by ID' })
  @ApiParam({ name: 'id', description: 'Age work ID' })
  @ApiResponse({ status: 200, description: 'Return the age work' })
  @ApiResponse({ status: 404, description: 'Age work not found' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.ageWorkService.findOneAgeWork(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an age work' })
  @ApiParam({ name: 'id', description: 'Age work ID' })
  @ApiBody({ type: UpdateAgeWorkDto })
  @ApiResponse({ status: 200, description: 'Age work successfully updated' })
  @ApiResponse({ status: 404, description: 'Age work not found' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateAgeWorkDto,
  ) {
    return this.ageWorkService.updateAgeWork(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an age work' })
  @ApiParam({ name: 'id', description: 'Age work ID' })
  @ApiResponse({ status: 200, description: 'Age work successfully deleted' })
  @ApiResponse({ status: 404, description: 'Age work not found' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.ageWorkService.removeAgeWork(id);
  }
}
