import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from '../../assessment-forms/entities/assessment.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('IDP_T_PROGRAMS',{ comment: 'โครงงาน' })
export class Program {
  @PrimaryGeneratedColumn({ name: 'PRG_ID', comment: 'รหัสโครงงาน' })
  id: number;

  @Column({ name: 'PRG_NAME', type: 'text', comment: 'ชื่อโครงงาน' })
  name: string;

  @Column({ name: 'PRG_DESCRIPTION', type: 'text', comment: 'รายละเอียดโครงงาน' })
  description: string;

  @ManyToOne(() => User, (user) => user.programs)
  @JoinColumn({ name: 'PRG_CREATOR_ID' })
  creator: User;

  @OneToMany(() => Assessment, (assessment) => assessment.program)
  assessments: Assessment[];
}
