// import { NestFactory } from '@nestjs/core';
// import { AppModule } from '../app.module';
// import { DataSource } from 'typeorm';
// import { seedSuperAdmin } from './seeds/superadmin.seed';
// import { seedBasicPermissions } from './seeds/basic-permissions.seed';
// import { seedCampus } from './seeds/campus.seed';
// import { seedFaculties } from './seeds/faculties.seed';
// import { seedDevelopmentPlans } from './seeds/development-plans.seed';
// import { seedDepartments } from './seeds/departments.seed';

// async function runSeeds() {
//   try {
//     console.log('Initializing NestJS application context...');
//     const app = await NestFactory.createApplicationContext(AppModule);
//     const dataSource = app.get(DataSource);

//     console.log('Starting database seeding...');
    
//     await seedCampus(dataSource);
//     await seedFaculties(dataSource);
//     await seedDevelopmentPlans(dataSource);
//     await seedDepartments(dataSource);  
//     await seedBasicPermissions(dataSource);
//     await seedSuperAdmin(dataSource);   // Create superadmin with env vars if not exists
    
//     console.log('Database seeding completed!');
    
//     await app.close();
//     process.exit(0);
//   } catch (error) {
//     console.error('Error during seeding:', error);
//     process.exit(1);
//   }
// }

// runSeeds();
