import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CareersService } from './careers.service';
import { CareersQueryDto } from './dto/careers-query.dto';
import { CareersResponseDto } from './dto/careers-response.dto';
import { Career } from './entities/careers.entity';
import { CreateCareerDto } from './dto/create-career.dto';
import { UpdateCareerDto } from './dto/update-career.dto';

@ApiTags('Careers')
@ApiBearerAuth()
@Controller('careers')
export class CareersController {
  constructor(private readonly careersService: CareersService) {}

  @Get()
  @ApiOperation({ summary: 'Get all careers with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns a paginated list of careers',
  })
  async findAll(@Query() query: CareersQueryDto): Promise<CareersResponseDto> {
    const [data, total] = await this.careersService.findAll(query);
    return new CareersResponseDto(data, total, query.page, query.limit);
  }

  @Get('names')
  @ApiOperation({
    summary: 'Get career names for dropdown with pagination and search',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns career names for dropdown',
  })
  async findCareerNamesForDropdown(@Query() query: CareersQueryDto): Promise<{
    data: string[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] =
      await this.careersService.findCareerNamesForDropdown(query);
    return {
      data,
      total,
      page: query.page || 1,
      limit: query.limit || 10,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single career by ID' })
  @ApiResponse({ status: 200, description: 'Returns the requested career' })
  @ApiResponse({ status: 404, description: 'Career not found' })
  async findOne(@Param('id') id: string): Promise<Career> {
    return this.careersService.findOne(+id);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new career' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The career has been successfully created',
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Bad request' })
  create(@Body() createCareerDto: CreateCareerDto): Promise<Career> {
    return this.careersService.create(createCareerDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a career' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The career has been successfully updated',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Career not found',
  })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCareerDto: UpdateCareerDto,
  ): Promise<Career> {
    return this.careersService.update(id, updateCareerDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a career' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The career has been successfully deleted',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Career not found',
  })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    await this.careersService.remove(id);
  }
}
