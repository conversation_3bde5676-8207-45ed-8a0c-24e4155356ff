import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsPositive,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ItemBlockType } from '../../enums/item-block-type.enum';
import { Type } from 'class-transformer';

export class CreateItemBlockDto {
  @ApiProperty({
    description: 'The order/position of this item block in the assessment',
    type: Number,
    minimum: 1,
    example: 1,
  })
  @Type(() => Number)
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  sequence: number;

  @ApiProperty({
    description: 'The section number this item block belongs to',
    type: Number,
    required: false,
    minimum: 1,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  section?: number;

  @ApiProperty({
    description: 'The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)',
    enum: ItemBlockType,
    enumName: 'ItemBlockType',
    example: ItemBlockType.RADIO,
  })
  @IsNotEmpty()
  @IsEnum(ItemBlockType)
  type: ItemBlockType;

  @ApiProperty({
    description: 'Whether this item block is required to be answered',
    type: Boolean,
    required: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiProperty({
    description: 'ID of the assessment this item block belongs to',
    type: Number,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  assessmentId: number;
}
