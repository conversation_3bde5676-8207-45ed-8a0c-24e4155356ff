npm run seed:test
# Database Seeders / ระบบ Seed ข้อมูล

This directory contains the modern seeding system for the backend using `typeorm-extension`.

ไดเรกทอรีนี้ประกอบด้วยระบบ seed ข้อมูลฐานข้อมูลแบบใหม่สำหรับ backend โดยใช้ `typeorm-extension` (รองรับ TypeORM 0.3.x)

## Directory Structure / โครงสร้างไดเรกทอรี

```
seeders/
├── main.seeder.ts          # Main seeder class (seed ข้อมูลหลัก)
├── seed.ts                 # Database seeding runner (รัน seed)
├── fixtures/               # JSON fixture files (ไฟล์ข้อมูล)
│   ├── permissions.json    # System permissions (สิทธิ์ระบบ)
│   ├── roles.json          # User roles (บทบาท)
│   ├── development-plans.json # Development plan templates (แผนพัฒนา)
│   └── users.json          # Default users (ผู้ใช้เริ่มต้น)
└── factories/              # Factory classes (สำหรับ test data)
```

## Usage / วิธีใช้งาน

### Seed all data (seed ข้อมูลทั้งหมด)

```bash
npm run seed
```

### Seed only superadmin (seed เฉพาะ superadmin)

```bash
npm run seed:admin
```

## Environment Variables / ตัวแปรสภาพแวดล้อม

Set these in your `.env.local` or `.env` file:

ตั้งค่าตัวแปรเหล่านี้ในไฟล์ `.env.local` หรือ `.env`:

```env
# Database connection (required) / การเชื่อมต่อฐานข้อมูล (จำเป็น)
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database

# Super admin credentials (optional) / รหัสผ่าน superadmin (ไม่บังคับ)
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_PASSWORD=your_secure_password

# Default user password (optional) / รหัสผ่านผู้ใช้เริ่มต้น (ไม่บังคับ)
DEFAULT_USER_PASSWORD=password123
```

## Fixture Data / ข้อมูลตัวอย่าง

### Users / ผู้ใช้

Seeding will create these users:

ระบบจะสร้างผู้ใช้เริ่มต้นดังนี้:

- System SuperAdmin (ผู้ดูแลระบบสูงสุด)
- Admin (ผู้ดูแล)
- Manager (ผู้จัดการ)
- Employee (พนักงาน)
- HR (ฝ่ายบุคคล)

### Roles & Permissions / บทบาทและสิทธิ์

- Super Admin: All permissions (ทุกสิทธิ์)
- Admin: Most CRUD operations (เกือบทุกสิทธิ์)
- Manager: Department-specific (เฉพาะแผนก)
- Employee: Basic read (อ่านข้อมูล)
- HR: HR-specific (ฝ่ายบุคคล)

### Development Plans / แผนพัฒนา

Pre-defined development plan templates for employee growth.
มีแผนพัฒนาบุคลากรตัวอย่างให้พร้อมใช้งาน

## Development / สำหรับนักพัฒนา

### Adding New Fixtures / เพิ่มข้อมูล fixture

1. Create a new JSON file in `fixtures/` directory
2. Add the fixture loading logic to `main.seeder.ts`
3. Create corresponding seeding method

1. สร้างไฟล์ JSON ใหม่ใน `fixtures/`
2. เพิ่ม logic โหลด fixture ใน `main.seeder.ts`
3. เขียน method สำหรับ seed ข้อมูลนั้น

### Adding New Entities / เพิ่ม Entity ใหม่

1. Import the entity in `main.seeder.ts`
2. Create seeding method for the entity
3. Add fixture data if needed
4. Call the method in the `run()` function

1. import entity ใน `main.seeder.ts`
2. เขียน method สำหรับ seed entity นั้น
3. เพิ่ม fixture data ถ้าจำเป็น
4. เรียกใช้ method ใน `run()`

## Troubleshooting / การแก้ไขปัญหา

### Common Issues / ปัญหาที่พบบ่อย

1. **Database connection errors**: Check environment variables
2. **Permission denied**: Ensure database user has proper privileges
3. **Duplicate entries**: The seeder checks for existing data before inserting
4. **Path resolution**: Make sure to run from the backend directory

1. **เชื่อมต่อฐานข้อมูลไม่ได้**: ตรวจสอบตัวแปรสภาพแวดล้อม
2. **สิทธิ์ไม่พอ**: ตรวจสอบสิทธิ์ของ user ฐานข้อมูล
3. **ข้อมูลซ้ำ**: ระบบจะเช็คก่อน insert
4. **path ไม่ถูกต้อง**: ให้รันคำสั่งในโฟลเดอร์ backend

### Logs / ข้อความ log

The seeder provides detailed logging:
- ✨ Creation of new records
- 📋 Skipping existing records  
- 🔗 Relationship assignments
- ⚠️ Warnings for missing references
- 🔑 Credential information for system users

ระบบจะแสดง log:
- ✨ สร้างข้อมูลใหม่
- 📋 ข้ามข้อมูลที่มีอยู่แล้ว
- 🔗 สร้างความสัมพันธ์ข้อมูล
- ⚠️ แจ้งเตือนถ้าข้อมูลอ้างอิงไม่เจอ
- 🔑 แสดงรหัสผ่าน superadmin (dev เท่านั้น)
