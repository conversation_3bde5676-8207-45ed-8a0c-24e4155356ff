import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateQuizSettingsDto {
  @ApiProperty({
    description: 'วันที่เริ่มต้น',
    type: 'string',
    format: 'date-time',
    example: new Date(),
    required: false,
  })
  @IsDate()
  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : undefined, { toClassOnly: true })
  startAt?: Date;

  @ApiProperty({
    description: 'วันที่สิ้นสุด',
    type: 'string',
    format: 'date-time',
    example: (() => {
      const date = new Date();
      date.setDate(date.getDate() + 1);
      return date;
    })(),
    required: false,
  })
  @IsDate()
  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : undefined, { toClassOnly: true })
  endAt?: Date;

  @ApiProperty({
    description: 'เวลาที่อนุญาต (วินาที)',
    example: 1800,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  submitLimit?: number;

  @ApiProperty({
    description: 'อัตราส่วนการผ่าน (เช่น 0.5, 0.8)',
    example: 0.6,
    type: 'number',
    format: 'float',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  passRatio?: number;

  @ApiProperty({
    description: 'เป็นต้นแบบหรือไม่',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(
    ({ value }) => {
      if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
      }
      return Boolean(value);
    },
    { toClassOnly: true },
  )
  isPrototype?: boolean;
}
