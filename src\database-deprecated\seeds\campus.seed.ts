import { DataSource } from 'typeorm';
import { Campus } from '../../resources/campus/entities/campus.entity';

export async function seedCampus(dataSource: DataSource) {
  const campusRepository = dataSource.getRepository(Campus);

  console.log('Starting campus seeding...');

  const campusData = [
    { id: '1', name: 'บางแสน' },
    { id: '2', name: 'จันทบุรี' },
    { id: '3', name: 'สระแก้ว' },
  ];

  for (const campusInfo of campusData) {
    const existingCampus = await campusRepository.findOne({
      where: { id: campusInfo.id }
    });

    if (!existingCampus) {
      const campus = campusRepository.create(campusInfo);
      await campusRepository.save(campus);
      console.log(`Created campus: ${campusInfo.name} (${campusInfo.id})`);
    } else {
      console.log(`Campus already exists: ${campusInfo.name} (${campusInfo.id})`);
    }
  }

  console.log('Campus seeding completed!');
}
