import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';
import { DataParams } from 'src/types/params';

export class ParticipantDetailsQueryDto implements DataParams {
  page: number;
  limit: number;
  sortBy: string | null;
  order: 'ASC' | 'DESC';
  search: string | null;

  @ApiPropertyOptional({
    description: 'กรองเฉพาะคำถามประเภท TEXTFIELD',
    example: 'true',
    enum: ['true', 'false'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['true', 'false'])
  @Transform(({ value }) => value?.toString())
  isTextField?: string;
}
