import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Not, IsNull } from 'typeorm';
import type { DataParams, DataResponse } from 'src/types/params';
import { Submission } from '../entities/submission.entity';
import { Question } from '../entities/question.entity';
import { Assessment } from '../entities/assessment.entity';
import { AssessmentValidator } from '../helper/assessments.validator';
import { QuizHelperService } from './quiz-helper.service';

// =============================================================================
// INTERFACES
// =============================================================================

export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: ChartData;
  textFieldAnalysis?: {
    totalResponders: number;
    duplicateAnswersCount: number;
    sampleDuplicateUsers: Array<{
      userId: number;
      userName: string;
      answer: string;
      duplicateCount: number;
    }>;
  };
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

// =============================================================================
// SERVICE CLASS
// =============================================================================

@Injectable()
export class QuizDashboardService {
  // Constants
  private readonly CHART_COLORS = [
    '#FF6384',
    '#36A2EB',
    '#FFCE56',
    '#4BC0C0',
    '#9966FF',
    '#FF9F40',
  ];

  private readonly SUBMISSION_RELATIONS = [
    'user',
    'assessment',
    'responses',
    'responses.selectedOption',
    'responses.question',
    'responses.question.itemBlock',
  ];

  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    private assessmentValidator: AssessmentValidator,
    private quizHelperService: QuizHelperService,
  ) {}

  // =============================================================================
  // PUBLIC API METHODS
  // =============================================================================

  async generateAssessmentMeta(assessmentId: number): Promise<AssessmentMeta> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const [assessment, uniqueUsers, scoreStats] = await Promise.all([
      this.assessmentRepository.findOne({ where: { id: assessmentId } }),
      this.getUniqueUsersCount(assessmentId),
      this.getScoreStatistics(assessmentId),
    ]);

    return {
      assessmentName: assessment?.name || `Assessment #${assessmentId}`,
      uniqueUsers,
      ...scoreStats,
    };
  }

  async generateAllQuestionResponses(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<QuestionResponseData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const questionsData = await this.getQuestionsForAssessment(
      assessmentId,
      query,
    );

    if (questionsData.total === 0) {
      return {
        data: [],
        total: 0,
        currentPage: questionsData.currentPage,
        itemsPerPage: questionsData.itemsPerPage,
        totalPages: questionsData.totalPages,
        hasPrev: false,
        hasNext: false,
      };
    }

    const responses = await Promise.all(
      questionsData.data.map((question) =>
        this.buildQuestionResponse(question),
      ),
    );

    return {
      data: responses,
      total: questionsData.total,
      currentPage: questionsData.currentPage,
      itemsPerPage: questionsData.itemsPerPage,
      totalPages: questionsData.totalPages,
      hasPrev: questionsData.hasPrev,
      hasNext: questionsData.hasNext,
    };
  }
  async getAllParticipants(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const [submissions, total] = await this.getSubmissionsWithScores(
      assessmentId,
      skip,
      Number(limit),
      search,
      sortBy,
      order,
    );

    const formattedParticipants = await this.formatParticipantData(
      submissions,
      assessmentId,
      sortBy,
      order,
    );

    return {
      data: formattedParticipants,
      total,
      currentPage: Number(page),
      itemsPerPage: Number(limit),
      totalPages: Math.ceil(total / Number(limit)),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async getOneParticipant(submissionId: number, query: DataParams) {
    const { page = 1, limit = 5 } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const submission = await this.getSubmissionBasicInfo(submissionId);
    if (!submission) {
      return null;
    }

    const { itemBlocks, total } = await this.getPaginatedItemBlocks(
      submissionId,
      skip,
      Number(limit),
    );

    const { userScore, maxScore } = await this.calculateSubmissionScores(
      submissionId,
      submission.assessmentId,
    );

    const responses = await this.getUserResponses(submissionId);
    const processedItemBlocks = this.processItemBlocksWithResponses(
      itemBlocks,
      responses,
    );

    const participantData = {
      submissionId: submission.id,
      assessmentId: submission.assessmentId,
      assessmentName: submission.assessment.name,
      userId: submission.user.id,
      userName: `${submission.user.firstNameTh} ${submission.user.lastNameTh}`.trim(),
      startTime: submission.startAt,
      endTime: submission.endAt,
      totalScore: userScore,
      maxScore: maxScore,
      scorePercentage: maxScore > 0 ? (userScore / maxScore) * 100 : 0,
      itemBlocks: processedItemBlocks,
    };

    return {
      data: [participantData],
      total: total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async getOneParticipantTextFieldGrading(
    submissionId: number,
    query: DataParams,
  ) {
    const { page = 1, limit = 5 } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const submission = await this.getSubmissionBasicInfo(submissionId);
    if (!submission) {
      return null;
    }

    const { itemBlocks, total } = await this.getPaginatedTextFieldItemBlocks(
      submissionId,
      skip,
      Number(limit),
    );

    const responses = await this.getTextFieldResponses(submissionId);
    const processedItemBlocks = this.processTextFieldItemBlocks(
      itemBlocks,
      responses,
    );

    const participantData = {
      submissionId: submission.id,
      assessmentId: submission.assessmentId,
      assessmentName: submission.assessment.name,
      userId: submission.user.id,
      userName: `${submission.user.firstNameTh} ${submission.user.lastNameTh}`.trim(),
      itemBlocks: processedItemBlocks,
    };

    return {
      data: [participantData],
      total: total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async saveTextFieldScore(
    submissionId: number,
    questionId: number,
    score: number,
  ): Promise<{ success: boolean; message: string }> {
    const submission = await this.submissionRepository.findOne({
      where: { id: submissionId },
      relations: [
        'responses',
        'responses.question',
        'responses.selectedOption',
      ],
    });

    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${submissionId} not found`,
      );
    }

    const existingResponse = submission.responses?.find(
      (response) => response.questionId === questionId,
    );

    if (!existingResponse) {
      throw new NotFoundException(
        `Response for question ${questionId} in submission ${submissionId} not found`,
      );
    }

    const question = await this.submissionRepository.manager
      .getRepository('Question')
      .createQueryBuilder('question')
      .leftJoin('question.itemBlock', 'itemBlock')
      .where('question.id = :questionId', { questionId })
      .andWhere('itemBlock.type = :type', { type: 'TEXTFIELD' })
      .getOne();

    if (!question) {
      throw new NotFoundException(
        `TEXTFIELD question with ID ${questionId} not found`,
      );
    }

    if (existingResponse.selectedOption) {
      await this.submissionRepository.manager
        .getRepository('Option')
        .update(existingResponse.selectedOption.id, {
          value: score,
        });

      return {
        success: true,
        message: `Score ${score} saved successfully for question ${questionId}`,
      };
    } else {
      throw new NotFoundException(
        `Selected option not found for response ${existingResponse.id}`,
      );
    }
  }

  // =============================================================================
  // PRIVATE HELPER METHODS
  // =============================================================================

  private async getSubmissionsWithScores(
    assessmentId: number,
    skip: number,
    limit: number,
    search?: string,
    sortBy?: string,
    order?: string,
  ) {
    const queryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.user', 'user')
      .leftJoinAndSelect('submission.assessment', 'assessment')
      .leftJoinAndSelect('submission.responses', 'responses')
      .leftJoinAndSelect('responses.selectedOption', 'selectedOption')
      .leftJoinAndSelect('responses.question', 'question')
      .leftJoinAndSelect('question.itemBlock', 'itemBlock')
      .where('submission.assessmentId = :assessmentId', { assessmentId });

    if (search) {
      queryBuilder.andWhere(
        '(user.firstName LIKE :search OR user.lastName LIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (sortBy === 'userName') {
      const orderDirection = (order || 'ASC').toUpperCase();
      queryBuilder.orderBy('user.firstName', orderDirection as 'ASC' | 'DESC')
                  .addOrderBy('user.lastName', orderDirection as 'ASC' | 'DESC');
    } else if (sortBy === 'date') {
      const orderDirection = (order || 'ASC').toUpperCase();
      queryBuilder.orderBy('submission.startAt', orderDirection as 'ASC' | 'DESC');
    } else if (sortBy && sortBy !== 'score') {
      const orderDirection = (order || 'ASC').toUpperCase();
      queryBuilder.orderBy(`submission.${sortBy}`, orderDirection as 'ASC' | 'DESC');
    } else {
      queryBuilder.orderBy('submission.id', 'ASC');
    }

    queryBuilder.take(limit).skip(skip);

    return await queryBuilder.getManyAndCount();
  }

  private async getSubmissionBasicInfo(submissionId: number) {
    return await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.user', 'user')
      .leftJoinAndSelect('submission.assessment', 'assessment')
      .where('submission.id = :submissionId', { submissionId })
      .getOne();
  }

  private async getPaginatedItemBlocks(
    submissionId: number,
    skip: number,
    limit: number,
  ) {
    const itemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type != :headerType', { headerType: 'HEADER' });

    const itemBlocksQuery = await itemBlocksQueryBuilder
      .select('itemBlock.id', 'itemBlockId')
      .orderBy('itemBlock.sequence', 'ASC')
      .offset(skip)
      .limit(limit)
      .getRawMany();

    const itemBlockIds = itemBlocksQuery
      .map((row) => row.itemBlockId)
      .filter((id) => id !== null);

    const itemBlocks =
      itemBlockIds.length > 0
        ? await this.submissionRepository.manager
            .getRepository('ItemBlock')
            .createQueryBuilder('itemBlock')
            .leftJoinAndSelect('itemBlock.questions', 'question')
            .leftJoinAndSelect('itemBlock.options', 'option')
            .whereInIds(itemBlockIds)
            .orderBy('itemBlock.sequence', 'ASC')
            .addOrderBy('question.sequence', 'ASC')
            .addOrderBy('option.sequence', 'ASC')
            .getMany()
        : [];

    const totalItemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type != :headerType', { headerType: 'HEADER' });

    const totalItemBlocks = await totalItemBlocksQueryBuilder
      .select('COUNT(DISTINCT itemBlock.id)', 'count')
      .getRawOne();

    const total = parseInt(totalItemBlocks?.count || '0');

    return { itemBlocks, total };
  }

  private async getPaginatedTextFieldItemBlocks(
    submissionId: number,
    skip: number,
    limit: number,
  ) {
    const itemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type = :textfieldType', {
        textfieldType: 'TEXTFIELD',
      });

    const itemBlocksQuery = await itemBlocksQueryBuilder
      .select('itemBlock.id', 'itemBlockId')
      .orderBy('itemBlock.sequence', 'ASC')
      .offset(skip)
      .limit(limit)
      .getRawMany();

    const itemBlockIds = itemBlocksQuery
      .map((row) => row.itemBlockId)
      .filter((id) => id !== null);

    const itemBlocks =
      itemBlockIds.length > 0
        ? await this.submissionRepository.manager
            .getRepository('ItemBlock')
            .createQueryBuilder('itemBlock')
            .leftJoinAndSelect('itemBlock.questions', 'question')
            .whereInIds(itemBlockIds)
            .orderBy('itemBlock.sequence', 'ASC')
            .addOrderBy('question.sequence', 'ASC')
            .getMany()
        : [];

    const totalItemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type = :textfieldType', {
        textfieldType: 'TEXTFIELD',
      });

    const totalItemBlocks = await totalItemBlocksQueryBuilder
      .select('COUNT(DISTINCT itemBlock.id)', 'count')
      .getRawOne();

    const total = parseInt(totalItemBlocks?.count || '0');

    return { itemBlocks, total };
  }

  private async calculateSubmissionScores(
    submissionId: number,
    assessmentId: number,
  ) {
    const submission = await this.submissionRepository.findOne({
      where: { id: submissionId },
      relations: {
        responses: {
          selectedOption: true,
          question: { itemBlock: true },
        },
        assessment: true,
      },
    });

    if (!submission) {
      return { userScore: 0, maxScore: 0 };
    }

    try {
      const maxScore =
        await this.quizHelperService.calculateTotalScore(assessmentId);
      const scoreResult = await this.quizHelperService.calculateScore(
        submission,
        submission.assessment,
        maxScore,
      );
      return { userScore: scoreResult.score, maxScore };
    } catch (error) {
      console.error(
        `Error calculating score for submission ${submissionId}:`,
        error,
      );
      return { userScore: 0, maxScore: 0 };
    }
  }

  private async getUserResponses(submissionId: number) {
    const submission = await this.submissionRepository.findOne({
      where: { id: submissionId },
      relations: [
        'responses',
        'responses.selectedOption',
        'responses.question',
      ],
    });

    return submission?.responses || [];
  }

  private async getTextFieldResponses(submissionId: number) {
    const submission = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.responses', 'response')
      .leftJoinAndSelect('response.selectedOption', 'selectedOption')
      .leftJoinAndSelect('response.question', 'responseQuestion')
      .leftJoinAndSelect('responseQuestion.itemBlock', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type = :textfieldType', {
        textfieldType: 'TEXTFIELD',
      })
      .getOne();

    return submission?.responses || [];
  }

  private processItemBlocksWithResponses(itemBlocks: any[], responses: any[]) {
    return itemBlocks.map((itemBlock) => {
      const blockResponses = responses.filter(
        (response) =>
          response.question &&
          itemBlock.questions?.some((q) => q.id === response.question.id),
      );

      const processedQuestions = (itemBlock.questions || []).map((question) => {
        const questionResponse = blockResponses.find(
          (r) => r.question?.id === question.id,
        );
        const selectedOption = questionResponse?.selectedOption;

        return {
          ...question,
          userResponse: questionResponse
            ? {
                id: questionResponse.id,
                selectedOptionId: selectedOption?.id,
                selectedOptionText: selectedOption?.optionText,
                textAnswer:
                  itemBlock.type === 'TEXTFIELD'
                    ? selectedOption?.optionText
                    : undefined,
                isCorrect: selectedOption ? selectedOption.value > 0 : false,
                score: selectedOption
                  ? itemBlock.type === 'TEXTFIELD'
                    ? selectedOption.value
                    : selectedOption.value * question.score
                  : 0,
              }
            : null,
        };
      });

      const processedOptions = (itemBlock.options || []).map((option) => {
        const isSelected = blockResponses.some(
          (r) => r.selectedOption?.id === option.id,
        );

        return {
          ...option,
          isSelected,
        };
      });

      return {
        ...itemBlock,
        questions: processedQuestions,
        options: processedOptions,
      };
    });
  }

  private processTextFieldItemBlocks(itemBlocks: any[], responses: any[]) {
    return itemBlocks.map((itemBlock) => {
      const blockResponses = responses.filter(
        (response) =>
          response.question &&
          itemBlock.questions?.some((q) => q.id === response.question.id),
      );

      const processedQuestions = (itemBlock.questions || []).map((question) => {
        const questionResponse = blockResponses.find(
          (r) => r.question?.id === question.id,
        );
        const selectedOption = questionResponse?.selectedOption;

        return {
          id: question.id,
          questionText: question.questionText,
          score: question.score,
          sequence: question.sequence,
          userResponse: questionResponse
            ? {
                id: questionResponse.id,
                textAnswer: selectedOption?.optionText,
                currentScore: selectedOption?.value || 0,
              }
            : null,
        };
      });

      return {
        id: itemBlock.id,
        type: itemBlock.type,
        sequence: itemBlock.sequence,
        questions: processedQuestions,
      };
    });
  }

  private async formatParticipantData(
    submissions: any[],
    assessmentId: number,
    sortBy?: string,
    order?: string,
  ) {
    const participants = [];

    for (const submission of submissions) {
      try {
        const maxScore =
          await this.quizHelperService.calculateTotalScore(assessmentId);
        const scoreResult = await this.quizHelperService.calculateScore(
          submission,
          submission.assessment,
          maxScore,
        );

        participants.push({
          id: submission.id,
          date: submission.startAt?.toISOString() || new Date().toISOString(),
          userName: submission.user?.firstName && submission.user?.lastName 
            ? `${submission.user.firstName} ${submission.user.lastName}`.trim()
            : 'Unknown User',
          score: scoreResult.score,
        });
      } catch (error) {
        console.error(
          `Error calculating score for submission ${submission.id}:`,
          error,
        );
        participants.push({
          id: submission.id,
          date: submission.startAt?.toISOString() || new Date().toISOString(),
          userName: submission.user?.firstName && submission.user?.lastName 
            ? `${submission.user.firstName} ${submission.user.lastName}`.trim()
            : 'Unknown User',
          score: 0,
        });
      }
    }

    if (sortBy === 'score') {
      const sortOrder = (order || 'ASC').toUpperCase();
      participants.sort((a, b) => {
        return sortOrder === 'DESC' ? b.score - a.score : a.score - b.score;
      });
    }

    return participants;
  }

  private buildOrderCondition(sortBy?: string, order?: string): any {
    const orderCondition: any = {};
    const orderDirection = (order || 'ASC').toUpperCase();

    if (sortBy === 'userName') {
      orderCondition.user = { name: orderDirection };
    } else if (sortBy === 'date') {
      orderCondition.startAt = orderDirection;
    } else if (sortBy && sortBy !== 'score') {
      orderCondition[sortBy] = orderDirection;
    } else {
      orderCondition.id = 'ASC';
    }

    return orderCondition;
  }

  private async getUniqueUsersCount(assessmentId: number): Promise<number> {
    const result = await this.submissionRepository
      .createQueryBuilder('submission')
      .select('COUNT(DISTINCT submission.userId)', 'uniqueUsers')
      .where('submission.assessmentId = :assessmentId', { assessmentId })
      .getRawOne();

    return parseInt(result?.uniqueUsers || '0');
  }

  private async getScoreStatistics(assessmentId: number) {
    const submissions = await this.submissionRepository.find({
      where: { assessmentId, submitAt: Not(IsNull()) },
      relations: {
        responses: {
          selectedOption: true,
          question: { itemBlock: true },
        },
        assessment: true,
      },
    });

    if (submissions.length === 0) {
      return { highestScore: 0, lowestScore: 0 };
    }

    const scores: number[] = [];
    for (const submission of submissions) {
      try {
        const totalScore =
          await this.quizHelperService.calculateTotalScore(assessmentId);
        const scoreResult = await this.quizHelperService.calculateScore(
          submission,
          submission.assessment,
          totalScore,
        );
        scores.push(scoreResult.score);
      } catch (error) {
        console.error(
          `Error calculating score for submission ${submission.id}:`,
          error,
        );
        scores.push(0);
      }
    }

    return {
      highestScore: Math.max(...scores),
      lowestScore: Math.min(...scores),
    };
  }

  private async getQuestionsForAssessment(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<any>> {
    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereCondition: any = {
      itemBlock: { assessment: { id: assessmentId } },
    };

    if (search) {
      whereCondition.questionText = Like(`%${search}%`);
    }

    const orderCondition = this.buildQuestionOrderCondition(sortBy, order);

    const [questions, total] = await this.questionRepository.findAndCount({
      where: whereCondition,
      relations: ['itemBlock', 'itemBlock.assessment'],
      order: orderCondition,
      take: Number(limit),
      skip,
    });

    const formattedQuestions = questions.map((question) => ({
      question_id: question.id,
      order_in_quiz: question.itemBlock.sequence,
      question_text: question.questionText,
      question_type: question.itemBlock.type,
    }));

    return {
      data: formattedQuestions,
      total,
      currentPage: Number(page),
      itemsPerPage: Number(limit),
      totalPages: Math.ceil(total / Number(limit)),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  private buildQuestionOrderCondition(sortBy?: string, order?: string): any {
    const orderCondition: any = {};
    const orderDirection = (order || 'ASC').toUpperCase();

    if (sortBy === 'orderInQuiz') {
      orderCondition.itemBlock = { sequence: orderDirection };
    } else if (sortBy) {
      orderCondition[sortBy] = orderDirection;
    } else {
      orderCondition.itemBlock = { sequence: 'ASC' };
    }

    return orderCondition;
  }

  private async buildQuestionResponse(
    question: any,
  ): Promise<QuestionResponseData> {
    let options: OptionResponseData[] = [];
    let chartData: ChartData;

    if (question.question_type === 'TEXTFIELD') {
      chartData = this.createEmptyChart();
    } else {
      options = await this.getQuestionOptions(question.question_id);
      chartData = this.createQuestionChart(options);
    }

    const responseData: QuestionResponseData = {
      questionId: question.question_id,
      orderInQuiz: question.order_in_quiz,
      questionText: question.question_text,
      questionType: question.question_type,
      options,
      chartData,
    };

    if (question.question_type === 'TEXTFIELD') {
      responseData.textFieldAnalysis = await this.analyzeTextFieldResponses(
        question.question_id,
      );
    }

    return responseData;
  }

  private async getQuestionOptions(
    questionId: number,
  ): Promise<OptionResponseData[]> {
    const optionsData = await this.questionRepository
      .createQueryBuilder('question')
      .leftJoin('question.itemBlock', 'itemBlock')
      .leftJoin('itemBlock.options', 'option')
      .leftJoin(
        'option.responses',
        'response',
        'response.questionId = :questionId',
        { questionId },
      )
      .select([
        'option.id as option_id',
        'option.sequence as order_in_question',
        'option.optionText as option_text',
        'COUNT(response.id) as selection_count',
        'option.value as option_value',
      ])
      .where('question.id = :questionId', { questionId })
      .groupBy('option.id, option.sequence, option.optionText, option.value')
      .orderBy('option.sequence', 'ASC')
      .getRawMany();

    return optionsData.map((result) => ({
      optionId: parseInt(result.option_id),
      orderInQuestion: parseInt(result.order_in_question),
      optionText: result.option_text,
      selectionCount: parseInt(result.selection_count || '0'),
      isCorrectAnswer: parseInt(result.option_value) === 1,
    }));
  }

  private createEmptyChart(): ChartData {
    return {
      labels: ['No Data'],
      datasets: [{ data: [1], backgroundColor: ['#e0e0e0'] }],
    };
  }

  private createQuestionChart(options: OptionResponseData[]): ChartData {
    return {
      labels: options.map((option) => option.optionText),
      datasets: [
        {
          data: options.map((option) => option.selectionCount),
          backgroundColor: options.map(
            (_, index) => this.CHART_COLORS[index % this.CHART_COLORS.length],
          ),
        },
      ],
    };
  }

  private async analyzeTextFieldResponses(questionId: number) {
    const responses = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.responses', 'response')
      .leftJoin('response.selectedOption', 'option')
      .leftJoin('submission.user', 'user')
      .select([
        'user.id as userId',
        'CONCAT(user.firstName, " ", user.lastName) as userName',
        'option.optionText as answer',
        'COUNT(*) OVER (PARTITION BY option.optionText) as duplicateCount',
      ])
      .where('response.questionId = :questionId', { questionId })
      .andWhere('option.optionText IS NOT NULL')
      .andWhere("option.optionText != ''")
      .getRawMany();

    const totalResponders = responses.length;

    const answerGroups = responses.reduce((acc, response) => {
      const answer = response.answer.trim().toLowerCase();
      if (!acc[answer]) {
        acc[answer] = [];
      }
      acc[answer].push({
        userId: parseInt(response.userId),
        userName: response.userName,
        answer: response.answer,
      });
      return acc;
    }, {});

    const duplicateAnswers = Object.entries(answerGroups).filter(
      ([_, users]: [string, any[]]) => users.length > 1,
    );

    const duplicateAnswersCount = duplicateAnswers.reduce(
      (total, [_, users]: [string, any[]]) => total + users.length,
      0,
    );

    const sampleDuplicateUsers = duplicateAnswers
      .slice(0, 5)
      .flatMap(([answer, users]: [string, any[]]) =>
        users.slice(0, 5).map((user) => ({
          ...user,
          duplicateCount: users.length,
        })),
      )
      .slice(0, 5);

    return {
      totalResponders,
      duplicateAnswersCount,
      sampleDuplicateUsers,
    };
  }
}
