import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypePlansService } from './type-plans.service';
import { TypePlansController } from './type-plans.controller';
import { TypePlan } from './entities/type-plan.entity';
import { DevelopmentPlan } from '../development-plans/entities/development-plan.entity';
import { AgeWork } from '../age-work/entities/age-work.entity';
import { Skill } from '../skills/entities/skill.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TypePlan, DevelopmentPlan, AgeWork, Skill]),
  ],
  controllers: [TypePlansController],
  providers: [TypePlansService],
  exports: [TypePlansService],
})
export class TypePlansModule {}
