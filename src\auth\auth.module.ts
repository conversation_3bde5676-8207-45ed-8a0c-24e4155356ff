import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './constants';
import { ApiModule } from 'src/api/api.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { GraylogService } from 'src/graylog/graylog.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/resources/users/entities/user.entity';

@Module({
  imports: [
    JwtModule.register({
      global: true,
      secret: jwtConstants.secret,
      // signOptions: { expiresIn: '3m' }, // test
      signOptions: { expiresIn: '3h' }, // may be have dlg ask; if want use -> refresh // try auto refresh
    }),
    CacheModule.register(),
    // GraylogModule.register({
    //   servers: [{ host: '*********', port: 12201 }],
    //   hostname: 'intern-dev-back', // Set your NestJS app's hostname
    //   facility: 'nestjs',
    // }),
    ApiModule,
    ConfigModule,
    TypeOrmModule.forFeature([User]),
  ],
  providers: [AuthService, GraylogService],
  controllers: [AuthController],
})
export class AuthModule {}
