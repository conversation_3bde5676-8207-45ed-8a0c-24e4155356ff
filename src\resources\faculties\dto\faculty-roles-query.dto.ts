import { <PERSON><PERSON>ptional, <PERSON>N<PERSON>ber, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FacultyRolesQueryDto {
  @ApiProperty({ required: false, description: 'Faculty ID to filter by' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  facultyId?: number;

  @ApiProperty({ required: false, description: 'Role ID to filter by' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  roleId?: number;

  @ApiProperty({ required: false, description: 'Page number', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ required: false, description: 'Number of items per page', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({ required: false, description: 'Search term' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ required: false, description: 'Field to sort by', default: 'id' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'id';

  @ApiProperty({ required: false, description: 'Sort order', enum: ['ASC', 'DESC'], default: 'ASC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  order?: 'ASC' | 'DESC' = 'ASC';
}