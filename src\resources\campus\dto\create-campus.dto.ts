import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class CreateCampusDto {
  @ApiProperty({
    description: 'รหัสวิทยาเขต',
    example: 'CAP001',
    maxLength: 10,
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'ชื่อวิทยาเขตภาษาไทย',
    example: 'วิทยาเขตกรุงเทพมหานคร',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  nameTh: string;

  @ApiProperty({
    description: 'ชื่อวิทยาเขตภาษาอังกฤษ',
    example: 'Bangkok Campus',
    maxLength: 255,
    required: false,
  })
  @IsString()
  @IsOptional()
  nameEn?: string;

  @ApiProperty({
    description: 'ที่อยู่วิทยาเขต',
    example: '123 ถนนสุขุมวิท แขวงคลองตัน เขตคลองตัน กรุงเทพมหานคร 10110',
    required: false,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'เบอร์โทรศัพท์',
    example: '02-123-4567',
    maxLength: 20,
    required: false,
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'สถานะการใช้งาน',
    example: true,
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  status?: boolean;
}
