import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { TypePlansService } from './type-plans.service';
import { CreateTypePlanDto } from './dto/create-type-plan.dto';
import { UpdateTypePlanDto } from './dto/update-type-plan.dto';
import { RemoveSkillDto, SelectSkillsDto } from './dto/utils.dto';
import { ApiBearerAuth, ApiBody } from '@nestjs/swagger';

@ApiBearerAuth()
@Controller('type-plans')
export class TypePlansController {
  constructor(private readonly typePlansService: TypePlansService) {}


  @Post()
  create(@Body() createTypePlanDto: CreateTypePlanDto) {
    return this.typePlansService.create(createTypePlanDto);
  }

  @Get()
  findAll() {
    return this.typePlansService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.typePlansService.findOne(+id);
  }

  @Post('select-skill')
  selectSkillToType(@Body() selectSkillsDto: SelectSkillsDto) {
    return this.typePlansService.selectSkillToType(selectSkillsDto);
  }

  @Delete('remove-skill')
  removeSkillFromType(@Body() removeSkillDto: RemoveSkillDto) {
    return this.typePlansService.removeSkillFromType(removeSkillDto);
  }
}
