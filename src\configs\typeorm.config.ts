import { TypeOrmModuleOptions } from '@nestjs/typeorm';

function isLocalMode(): boolean {
  return process.env.AUTOMATE_TEST_MODE === 'true' || process.env.LOCAL === 'true';
}

export function getTypeOrmConfig(): TypeOrmModuleOptions {
  return {
    type: 'mariadb',
    host: isLocalMode() ? process.env.DB_TEST_HOST : process.env.DB_HOST,
    port: isLocalMode() ? +process.env.DB_TEST_PORT : +process.env.DB_PORT,
    username: isLocalMode() ? process.env.DB_TEST_USER : process.env.DB_USER,
    password: isLocalMode() ? process.env.DB_TEST_PASSWORD : process.env.DB_PASSWORD,
    database: isLocalMode() ? process.env.DB_TEST_NAME : process.env.DB_NAME,
    synchronize: isLocalMode(),
    migrationsRun: false,
    autoLoadEntities: true,
    entities: [
      __dirname + '/../**/*.entity{.ts,.js}',
    ],
  };
}
