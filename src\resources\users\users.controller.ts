import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { UsersService, UserListItem } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto, UpdateUserRolesDto } from './dto/update-user.dto';
import { UpdateUserDepartmentsDto } from '../faculties/dto/update-faculty-user.dto';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import type { DataParams, DataResponse } from 'src/types/params';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';
import { UpdateRoleDto } from '../roles/dto/update-role.dto';

@ApiBearerAuth()
@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @RequirePermissions('user_read')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all users' })
  @ApiQuery({
    name: 'facultyFilter',
    required: false,
    description: 'Filter users by faculty ID',
    type: 'string',
  })
  @ApiQuery({
    name: 'roleFilter',
    required: false,
    description: 'Filter users by role ID',
    type: 'string',
  })
  @ApiResponse({ status: 200, description: 'Return all users' })
  async findAll(
    @Query() query: DataParams,
    @Query('facultyFilter') facultyFilter?: string,
    @Query('roleFilter') roleFilter?: string,
  ): Promise<DataResponse<UserListItem>> {
    const facultyId = facultyFilter;
    const roleId = roleFilter ? +roleFilter : undefined;
    return this.usersService.findAll(query, facultyId, roleId);
  }

  @Get(':id')
  @RequirePermissions('user_read')
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Return the user' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @Delete(':id')
  @RequirePermissions('user_delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 204, description: 'User successfully deleted' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
  }

  @Get(':id/faculties')
  @RequirePermissions('user_read')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all faculties with user status for dialog' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Return all faculties with user status',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getFacultiesWithUserStatus(
    @Param('id') id: string,
    @Query() query: DataParams,
  ) {
    return this.usersService.getFacultiesWithUserStatus(id, query);
  }

  @Patch(':id/faculties')
  @RequirePermissions('user_update')
  @ApiOperation({
    summary: 'Update user faculties based on checkbox selection',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({
    type: UpdateUserDepartmentsDto,
    examples: {
      updateFaculties: {
        value: {
          departmentIds: ['DEP001', 'DEP002', 'DEP003'],
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'User faculties successfully updated',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserFaculties(
    @Param('id') id: string,
    @Body() updateUserDepartmentsDto: UpdateUserDepartmentsDto,
  ) {
    return this.usersService.updateUserFaculties(
      id,
      updateUserDepartmentsDto.departmentIds,
    );
  }

  @Patch(':id/roles')
  @RequirePermissions('user_update')
  @ApiOperation({ summary: 'Update user roles based on checkbox selection' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({
    type: UpdateUserRolesDto,
    examples: {
      UpdateRole: {
        value: {
          roleIds: [1, 2, 3],
          facultyUserId: 1,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'User faculties roles successfully updated',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserFacultiesRoles(
    @Param('id') id: string,
    @Body() updateUserRolesDto: UpdateUserRolesDto,
  ) {
    return this.usersService.updateRoles(
      id,
      updateUserRolesDto.roleIds,
      updateUserRolesDto.departmentId,
    );
  }

  @Get(':id/facultiesUser')
  @RequirePermissions('user_read')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all faculties of User' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Return all faculties with user status',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getFacultiesUser(@Param('id') id: string, @Query() query: DataParams) {
    return this.usersService.getUserFacultyUsers(id, query);
  }

  // This endpoint has been moved to FacultiesController
  // Use /faculties/roles instead
}
