import { User } from "src/resources/users/entities/user.entity";
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Skill } from "./skill.entity";

@Entity('IDP_T_USER_SKILLS', { comment: 'ทักษะของผู้ใช้' })
export class UserSkill {
  @PrimaryGeneratedColumn({ name: 'USK_ID', comment: 'รหัสทักษะของผู้ใช้' })
  id: number;

  @ManyToOne(() => User, (user) => user.userSkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'PSN_ID' })
  user: User;

  @ManyToOne(() => Skill, (skill) => skill.userSkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'SKILL_ID' })
  skill: Skill;

  @Column({ name: 'USK_IS_EXTRA', default: false, comment: 'ทักษะเพิ่มเติม' })
  is_extra: boolean;

  @Column({
    name: 'USK_STATUS',
    type: 'enum',
    enum: ['รออนุมัติ', 'อนุมัติ', 'ไม่อนุมัติ', 'รอแก้ไข', 'พักตอบ'],
    default: 'รออนุมัติ',
    comment: 'สถานะการอนุมัติทักษะ'
  })
  status: 'รออนุมัติ' | 'อนุมัติ' | 'ไม่อนุมัติ' | 'รอแก้ไข' | 'พักตอบ';

  @Column({ name: 'USK_APPROVED_AT', nullable: true, comment: 'วันที่อนุมัติ' })
  approved_at: Date;
}
