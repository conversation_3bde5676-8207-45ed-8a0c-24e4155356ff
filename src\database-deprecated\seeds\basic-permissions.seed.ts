import { DataSource } from 'typeorm';
import { Permission } from '../../resources/permissions/entities/permission.entity';

export async function seedBasicPermissions(dataSource: DataSource) {
  const permissionRepository = dataSource.getRepository(Permission);

  console.log('Starting basic permissions seeding...');

  const basicPermissions = [
    { name: 'user_create', nameEn: 'Create Users', status: true, isDefault: false },
    { name: 'user_read', nameEn: 'Read Users', status: true, isDefault: false },
    { name: 'user_update', nameEn: 'Update Users', status: true, isDefault: false },
    { name: 'user_delete', nameEn: 'Delete Users', status: true, isDefault: false },
    { name: 'role_create', nameEn: 'Create Roles', status: true, isDefault: false },
    { name: 'role_read', nameEn: 'Read Roles', status: true, isDefault: false },
    { name: 'role_update', nameEn: 'Update Roles', status: true, isDefault: false },
    { name: 'role_delete', nameEn: 'Delete Roles', status: true, isDefault: false },
    { name: 'permission_create', nameEn: 'Create Permissions', status: true, isDefault: false },
    { name: 'permission_read', nameEn: 'Read Permissions', status: true, isDefault: false },
    { name: 'permission_update', nameEn: 'Update Permissions', status: true, isDefault: false },
    { name: 'permission_delete', nameEn: 'Delete Permissions', status: true, isDefault: false },
    { name: 'assessment_create', nameEn: 'Create Assessments', status: true, isDefault: false },
    { name: 'assessment_read', nameEn: 'Read Assessments', status: true, isDefault: false },
    { name: 'assessment_update', nameEn: 'Update Assessments', status: true, isDefault: false },
    { name: 'assessment_delete', nameEn: 'Delete Assessments', status: true, isDefault: false },
  ];

  for (const permissionData of basicPermissions) {
    const existingPermission = await permissionRepository.findOne({
      where: { name: permissionData.name }
    });

    if (!existingPermission) {
      const permission = permissionRepository.create(permissionData);
      await permissionRepository.save(permission);
      console.log(`Created permission: ${permissionData.name}`);
    } else {
      console.log(`Permission already exists: ${permissionData.name}`);
    }
  }

  console.log('Basic permissions seeding completed!');
}
