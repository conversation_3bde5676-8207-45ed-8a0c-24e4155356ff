import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { CreateAgeWorkDto, UpdateAgeWorkDto } from './dto/age-work.dto';
import {
  CreateAgeWorkCriteriaDto,
  UpdateAgeWorkCriteriaDto,
} from './dto/age-work-criteria.dto';
import { AgeWork } from './entities/age-work.entity';
import { AgeWorkCriteria } from './entities/age-work-criteria.entity';
import type { DataParams, DataResponse } from 'src/types/params';

@Injectable()
export class AgeWorkService {
  constructor(
    @InjectRepository(AgeWork)
    private ageWorkRepository: Repository<AgeWork>,
    @InjectRepository(AgeWorkCriteria)
    private ageWorkCriteriaRepository: Repository<AgeWorkCriteria>,
  ) {}

  private buildPaginationResponse<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): DataResponse<T> {
    return {
      data,
      total,
      currentPage: page,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      hasPrev: page > 1,
      hasNext: page * limit < total,
    };
  }

  private buildSearchWhereClause(
    search: string | null | undefined,
    field: string,
  ) {
    return search ? { [field]: Like(`%${search}%`) } : {};
  }

  private buildOrderClause(
    sortBy: string | null | undefined,
    order: 'ASC' | 'DESC',
  ) {
    return { [sortBy || 'id']: order || 'ASC' };
  }

  private buildPaginationOptions(page: number, limit: number) {
    return {
      skip: (page - 1) * limit,
      take: limit,
    };
  }

  async createAgeWorkCriteria(
    createDto: CreateAgeWorkCriteriaDto,
  ): Promise<AgeWorkCriteria> {
    const criteria = this.ageWorkCriteriaRepository.create(createDto);
    return await this.ageWorkCriteriaRepository.save(criteria);
  }

  async findAllAgeWorkCriteria(
    pag: DataParams,
  ): Promise<DataResponse<AgeWorkCriteria>> {
    const [criteria, total] = await this.ageWorkCriteriaRepository.findAndCount(
      {
        relations: ['ageWorks'],
        where: this.buildSearchWhereClause(pag.search, 'name'),
        order: this.buildOrderClause(pag.sortBy, pag.order),
        ...this.buildPaginationOptions(pag.page, pag.limit),
      },
    );

    return this.buildPaginationResponse(criteria, total, pag.page, pag.limit);
  }

  async findOneAgeWorkCriteria(id: number): Promise<AgeWorkCriteria> {
    const criteria = await this.ageWorkCriteriaRepository.findOne({
      where: { id },
      relations: ['ageWorks'],
    });

    if (!criteria) {
      throw new NotFoundException(`AgeWorkCriteria with ID ${id} not found`);
    }

    return criteria;
  }

  async updateAgeWorkCriteria(
    id: number,
    updateDto: UpdateAgeWorkCriteriaDto,
  ): Promise<AgeWorkCriteria> {
    const criteria = await this.findOneAgeWorkCriteria(id);
    Object.assign(criteria, updateDto);
    return await this.ageWorkCriteriaRepository.save(criteria);
  }

  async removeAgeWorkCriteria(id: number): Promise<void> {
    const criteria = await this.findOneAgeWorkCriteria(id);
    await this.ageWorkCriteriaRepository.remove(criteria);
  }

  private async validateCriteriaExists(criteriaId: number): Promise<void> {
    await this.findOneAgeWorkCriteria(criteriaId);
  }

  async createAgeWork(createDto: CreateAgeWorkDto): Promise<AgeWork> {
    await this.validateCriteriaExists(createDto.ageWorkCriteriaId);

    const ageWork = this.ageWorkRepository.create(createDto);
    return await this.ageWorkRepository.save(ageWork);
  }

  async findOneAgeWork(id: number): Promise<AgeWork> {
    const ageWork = await this.ageWorkRepository.findOne({
      where: { id },
      relations: ['ageWorkCriteria'],
    });

    if (!ageWork) {
      throw new NotFoundException(`AgeWork with ID ${id} not found`);
    }

    return ageWork;
  }

  async updateAgeWork(
    id: number,
    updateDto: UpdateAgeWorkDto,
  ): Promise<AgeWork> {
    const ageWork = await this.findOneAgeWork(id);

    if (updateDto.ageWorkCriteriaId) {
      await this.validateCriteriaExists(updateDto.ageWorkCriteriaId);
    }

    Object.assign(ageWork, updateDto);
    return await this.ageWorkRepository.save(ageWork);
  }

  async removeAgeWork(id: number): Promise<void> {
    const ageWork = await this.findOneAgeWork(id);
    await this.ageWorkRepository.remove(ageWork);
  }

  async findAgeWorkByCriteria(
    criteriaId: number,
    pag: DataParams,
  ): Promise<DataResponse<AgeWork>> {
    const [ageWorks, total] = await this.ageWorkRepository.findAndCount({
      where: { ageWorkCriteriaId: criteriaId },
      relations: ['ageWorkCriteria'],
      order: this.buildOrderClause(pag.sortBy, pag.order),
      ...this.buildPaginationOptions(pag.page, pag.limit),
    });

    return this.buildPaginationResponse(ageWorks, total, pag.page, pag.limit);
  }
}
