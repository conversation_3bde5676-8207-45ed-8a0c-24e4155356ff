import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';
import { HeaderBody } from '../entities/header-body.entity';
import { ImageBody } from '../entities/image-body.entity';
import { AssessmentType } from '../enums/assessment-type.enum';
import { ApiService } from 'src/api/api.service';
import type { DataParams, DataResponse } from 'src/types/params';
import { FileUploadService } from '../utils/file-upload.service';

@Injectable()
export class AssessmentHelper {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    private readonly apiService: ApiService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  // === QUERY BUILDER OPERATIONS ===

  createBaseQueryBuilder(type: AssessmentType): SelectQueryBuilder<Assessment> {
    return this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type });
  }

  applySearchFilter(qb: SelectQueryBuilder<Assessment>, search?: string): void {
    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }
  }

  applySorting(
    qb: SelectQueryBuilder<Assessment>,
    sortBy?: string,
    order?: string,
  ): void {
    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }
  }

  async executePagedQuery(
    qb: SelectQueryBuilder<Assessment>,
    query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    qb.skip((page - 1) * limit).take(limit);
    const [data, total] = await qb.getManyAndCount();

    return this.buildPagedResponse(data, total, page, limit);
  }

  buildPagedResponse(
    data: Assessment[],
    total: number,
    page: number,
    limit: number,
  ): DataResponse<Assessment> {
    const totalPages = Math.ceil(total / limit);
    return {
      data,
      total,
      currentPage: page,
      itemsPerPage: limit,
      totalPages,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  // === ASSESSMENT OPERATIONS ===

  getFullRelations(): string[] {
    return [
      'creator',
      'program',
      'itemBlocks',
      'itemBlocks.questions',
      'itemBlocks.options',
      'itemBlocks.headerBody',
      'itemBlocks.imageBody',
    ];
  }

  async findAssessmentWithRelations(id: number): Promise<Assessment | null> {
    return this.assessmentRepository.findOne({
      where: { id },
      relations: this.getFullRelations(),
    });
  }

  async findAssessmentByLinkURL(linkURL: string): Promise<Assessment | null> {
    return this.assessmentRepository.findOne({
      where: { linkURL },
      relations: this.getFullRelations(),
    });
  }

  async findAssessmentBySection(
    id: number,
    section: number,
  ): Promise<Assessment | null> {
    return this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.itemBlocks', 'itemBlocks')
      .leftJoinAndSelect('itemBlocks.questions', 'questions')
      .leftJoinAndSelect('itemBlocks.imageBody', 'imageBody')
      .leftJoinAndSelect('itemBlocks.options', 'options')
      .leftJoinAndSelect('itemBlocks.headerBody', 'headerBody')
      .where('assessment.id = :id', { id })
      .andWhere('assessment.type = :type', { type: AssessmentType.EVALUATE })
      .andWhere('itemBlocks.section = :section', { section })
      .getOne();
  }

  // === DUPLICATION OPERATIONS ===

  async clearAssessmentBlocks(assessmentId: number): Promise<void> {
    await this.itemBlockRepository.delete({ assessmentId });
  }

  duplicateItemBlocks(
    sourceBlocks: ItemBlock[],
    targetAssessment: Assessment,
  ): ItemBlock[] {
    return sourceBlocks.map((sourceBlock) => {
      const newBlock = this.createNewItemBlock(sourceBlock, targetAssessment);
      this.duplicateBlockRelations(sourceBlock, newBlock);
      return newBlock;
    });
  }

  private createNewItemBlock(
    sourceBlock: ItemBlock,
    targetAssessment: Assessment,
  ): ItemBlock {
    const newBlock = new ItemBlock();
    newBlock.sequence = sourceBlock.sequence;
    newBlock.section = sourceBlock.section;
    newBlock.type = sourceBlock.type;
    newBlock.isRequired = sourceBlock.isRequired;
    newBlock.assessment = targetAssessment;
    return newBlock;
  }

  private duplicateBlockRelations(
    sourceBlock: ItemBlock,
    newBlock: ItemBlock,
  ): void {
    if (sourceBlock.headerBody) {
      newBlock.headerBody = this.duplicateHeaderBody(sourceBlock.headerBody);
    }

    if (sourceBlock.imageBody) {
      newBlock.imageBody = this.duplicateImageBody(sourceBlock.imageBody);
    }

    newBlock.options =
      sourceBlock.options?.map((opt) => this.duplicateOption(opt)) || [];
    newBlock.questions =
      sourceBlock.questions?.map((q) => this.duplicateQuestion(q)) || [];
  }

  private duplicateHeaderBody(headerBody: HeaderBody): HeaderBody {
    const newHeader = new HeaderBody();
    newHeader.title = headerBody.title;
    newHeader.description = headerBody.description;
    return newHeader;
  }

  private duplicateImageBody(imageBody: ImageBody): ImageBody {
    const newImage = new ImageBody();
    newImage.imagePath = imageBody.imagePath;
    newImage.imageText = imageBody.imageText;
    newImage.imageWidth = imageBody.imageWidth;
    newImage.imageHeight = imageBody.imageHeight;
    return newImage;
  }

  private duplicateOption(option: Option): Option {
    const newOption = new Option();
    newOption.optionText = option.optionText;
    newOption.imagePath = option.imagePath;
    newOption.value = option.value;
    newOption.sequence = option.sequence;
    newOption.nextSection = option.nextSection;
    return newOption;
  }

  private duplicateQuestion(question: Question): Question {
    const newQuestion = new Question();
    newQuestion.questionText = question.questionText;
    newQuestion.imagePath = question.imagePath;
    newQuestion.imageWidth = question.imageWidth;
    newQuestion.imageHeight = question.imageHeight;
    newQuestion.isHeader = question.isHeader;
    newQuestion.sequence = question.sequence;
    newQuestion.sizeLimit = question.sizeLimit;
    newQuestion.acceptFile = question.acceptFile;
    newQuestion.uploadLimit = question.uploadLimit;
    newQuestion.score = question.score;
    return newQuestion;
  }

  // === IMAGE PROCESSING ===

  async processAssessmentImages(assessment: Assessment): Promise<void> {
    await Promise.all(
      assessment.itemBlocks.map(async (itemBlock) => {
        await this.processBlockImages(itemBlock);
      }),
    );
  }

  private async processBlockImages(itemBlock: ItemBlock): Promise<void> {
    if (itemBlock.imageBody?.imagePath) {
      itemBlock.imageBody.imagePath =
        await this.fileUploadService.processImagePath(
          itemBlock.imageBody.imagePath,
        );
    }

    if (itemBlock.options?.length) {
      await Promise.all(
        itemBlock.options.map(async (option) => {
          option.imagePath = await this.fileUploadService.processImagePath(
            option.imagePath,
          );
        }),
      );
    }

    if (itemBlock.questions?.length) {
      await Promise.all(
        itemBlock.questions.map(async (question) => {
          question.imagePath = await this.fileUploadService.processImagePath(
            question.imagePath,
          );
        }),
      );
    }
  }

  // === RESPONSE BUILDERS ===

  buildAssessmentResponse(assessment: Assessment): Assessment {
    return {
      ...assessment,
      creator: assessment.creator,
      program: assessment.program,
      submissions: [],
      isPrototype: assessment.isPrototype ?? false,
    } as Assessment;
  }

  buildDuplicationResponse(assessment: Assessment): any {
    return {
      id: assessment.id,
      name: assessment.name,
      type: assessment.type,
    };
  }

  // === DASHBOARD OPERATIONS ===

  async getAssessmentCount(type: AssessmentType): Promise<{ total: number }> {
    const total = await this.assessmentRepository.count({ where: { type } });
    return { total };
  }
}
