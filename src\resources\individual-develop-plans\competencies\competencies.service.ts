import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { Competency } from './entities/competencies.entity';
import { CompetencySkill } from 'src/resources/individual-develop-plans/skills/entities/competency-skill.entity';
import { Skill } from 'src/resources/individual-develop-plans/skills/entities/skill.entity';
import { CreateCompetenciesDto } from './dto/create-competencies.dto';
import { UpdateCompetenciesDto } from './dto/update-competencies.dto';
import type { DataParams, DataResponse } from 'src/types/params';
import { instanceToPlain } from 'class-transformer';
import { CompetencyType } from './enum/competencies-type.enum';

@Injectable()
export class CompetenciesService {
  constructor(
    @InjectRepository(Competency)
    private competencyRepository: Repository<Competency>,
    @InjectRepository(CompetencySkill)
    private competencySkillRepository: Repository<CompetencySkill>,
    @InjectRepository(Skill)
    private skillRepository: Repository<Skill>,
  ) {}

  async create(createDto: CreateCompetenciesDto) {
    if (typeof createDto.career_type === 'string') {
      createDto.career_type = createDto.career_type as any;
    }

    // --- robust skillIds handling ---
    let skillIds: number[] = [];
    if (Array.isArray(createDto.skillIds)) {
      skillIds = createDto.skillIds.map(Number);
    } else if (
      createDto.skillIds &&
      typeof (createDto.skillIds as any) === 'string'
    ) {
      skillIds = (createDto.skillIds as any)
        .split(',')
        .map((s: string) => s.trim())
        .filter((s: string) => Boolean(s))
        .map(Number);
    }

    const { skillIds: _omit, ...competencyData } = createDto;
    const competency = this.competencyRepository.create(competencyData);
    const savedCompetency = await this.competencyRepository.save(competency);

    // === บันทึก CompetencySkills ===
    if (skillIds.length) {
      const foundSkills = await this.skillRepository.find({
        where: { id: In(skillIds) },
      });
      if (foundSkills.length !== skillIds.length) {
        throw new Error('Some skillIds are invalid');
      }
      const mappings = skillIds.map((skillId) =>
        this.competencySkillRepository.create({
          competency: savedCompetency,
          skill: { id: skillId },
        }),
      );
      await this.competencySkillRepository.save(mappings);
    }

    return instanceToPlain(savedCompetency);
  }

  //   async findAll(pag: DataParams): Promise<DataResponse<Competency>> {
  //     const [items, total] = await this.competencyRepository.findAndCount({
  //       where: {
  //         name: pag.search ? Like(`%${pag.search}%`) : undefined,
  //       },
  //       order: {
  //         [pag.sortBy || 'id']: pag.order || 'ASC',
  //       },
  //       skip: (pag.page - 1) * pag.limit,
  //       take: pag.limit,
  //       cache: true,
  //     });

  //     return {
  //       data: items,
  //       total,
  //       curPage: pag.page,
  //       hasNext: total > pag.page * pag.limit,
  //       hasPrev: pag.page > 1,
  //     };
  //   }

  async findAll(
    query: DataParams,
    career_type?: string,
  ): Promise<DataResponse<Competency>> {
    const where: any = {};

    if (query.search) {
      where.name = Like(`%${query.search}%`);
    }

    if (career_type) {
      where.career_type = career_type;
    }

    const [items, total] = await this.competencyRepository.findAndCount({
      where,
      order: {
        [query.sortBy || 'id']: query.order || 'ASC',
      },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
      cache: true,
    });

    return {
      data: items,
      total,
      currentPage: query.page,
      itemsPerPage: query.limit,
      totalPages: Math.ceil(total / query.limit),
      hasNext: total > query.page * query.limit,
      hasPrev: query.page > 1,
    };
  }

  async findOne(id: number) {
    const competency = await this.competencyRepository.findOne({
      where: { id },
    });
    if (!competency) {
      throw new NotFoundException('Competency not found');
    }
    // ดึง skills ที่ผูกกับ competency นี้
    const mappings = await this.competencySkillRepository.find({
      where: { competency: { id } },
      relations: ['skill'],
    });
    const skills = mappings.map((m) => m.skill);
    // return object ที่รวม skills เฉพาะใน findOne เท่านั้น
    return instanceToPlain({ ...competency, skills });
  }

  async findByType(career_type: CompetencyType): Promise<Competency[]> {
    const competencies = await this.competencyRepository.find({
      where: { career_type },
    });

    // ไม่ throw error ถ้าไม่เจอข้อมูล ให้ return [] แทน
    return competencies || [];
  }

  async update(id: number, updateDto: UpdateCompetenciesDto) {
    const competency = await this.findOne(id);

    if (typeof updateDto.career_type === 'string') {
      updateDto.career_type = updateDto.career_type as any;
    }

    const updated = { ...competency, ...updateDto };
    return this.competencyRepository.save(updated);
  }

  async remove(id: number) {
    const competency = await this.findOne(id);
    // remove ต้องใช้ entity competency เดิม ไม่ใช่ object ที่ merge skills แล้ว
    return this.competencyRepository.remove(
      await this.competencyRepository.findOneBy({ id }),
    );
  }
}
