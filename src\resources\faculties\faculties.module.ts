import { Module } from '@nestjs/common';
import { FacultiesService } from './faculties.service';
import { FacultiesController } from './faculties.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Faculty } from './entities/faculty.entity';
import { FacDepUserRoles } from './entities/faculty-deparment-user-role.entity';
import { User } from '../users/entities/user.entity';
import { Role } from '../roles/entities/role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Faculty, FacDepUserRoles, User, Role])], // Add your entity here
  controllers: [FacultiesController],
  providers: [FacultiesService],
})
export class FacultiesModule {}
