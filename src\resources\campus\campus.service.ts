import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campus } from './entities/campus.entity';
import { CreateCampusDto } from './dto/create-campus.dto';
import { UpdateCampusDto } from './dto/update-campus.dto';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';

@Injectable()
export class CampusService {
  constructor(
    @InjectRepository(Campus)
    private readonly campusRepository: Repository<Campus>,
  ) {}

  async create(createCampusDto: CreateCampusDto): Promise<Campus> {
    const campus = this.campusRepository.create(createCampusDto);
    return await this.campusRepository.save(campus);
  }

  async findAll(query: PaginationQueryDto): Promise<PaginationResponseDto<Campus>> {
    const { page = 1, limit = 10, search } = query;
    const queryBuilder = this.campusRepository.createQueryBuilder('campus');

    if (search) {
      queryBuilder.where('campus.nameTh LIKE :search OR campus.nameEn LIKE :search', { 
        search: `%${search}%` 
      });
    }

    const [data, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return new PaginationResponseDto(data, total, page, limit);
  }

  async findOne(id: string): Promise<Campus> {
    const campus = await this.campusRepository.findOne({
      where: { id },
      relations: ['faculties'],
    });

    if (!campus) {
      throw new NotFoundException(`Campus with ID ${id} not found`);
    }

    return campus;
  }

  async update(id: string, updateCampusDto: UpdateCampusDto): Promise<Campus> {
    const campus = await this.findOne(id);
    Object.assign(campus, updateCampusDto);
    return await this.campusRepository.save(campus);
  }

  async remove(id: string): Promise<void> {
    const campus = await this.findOne(id);
    await this.campusRepository.remove(campus);
  }
}