import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { CareerRecords } from '../../career-records/entites/career-records.entity';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_T_CAREERS', {comment: 'ข้อมูลอาชีพของบุคลากร (Career Information)'})
export class Career {
  @PrimaryGeneratedColumn({ name: 'CR_ID', comment: 'รหัสอาชีพ' })
  id: number;

  @Column({ name: 'GRL_NAMETH', comment: 'ประเภทอาชีพ (วิชาการ/สนับสนุน)' , length: 50 })
  career_type: string;

  @Column({ name: 'LIW_NAMETH', comment: 'ชื่ออาชีพ (อาจารย์/นักบัญชี)' , length: 255 })
  career_name: string;

  @Column({ name: 'RAS_NAMETH', comment: 'ระดับตำแหน่ง (ผู้ช่วยศาสตราจารย์/ชำนาญการ)' , length: 255 })
  career_rank: string;

  @JoinColumn({ name: 'CR_CRR_ID' })
  @OneToMany(() => CareerRecords, (cr) => cr.career_Id)
  careerRecords: CareerRecords[];

  @OneToMany(() => TypePlan, (typePlan) => typePlan.career)
  typePlans: TypePlan[];
}
