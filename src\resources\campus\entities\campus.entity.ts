import { Faculty } from 'src/resources/faculties/entities/faculty.entity';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity('HRD_T_CAMPUS', { comment: 'ตารางวิทยาเขต' })
export class Campus {
  @Column({ name: 'CAP_ID', comment: 'รหัสวิทยาเขต', primary: true, nullable: false, length: 10 })
  id: string;

  @Column({ name: 'CAP_NAMETH', nullable: false, comment: 'ชื่อวิทยาเขตภาษาไทย', length: 255 })
  name: string;

  @OneToMany(
    () => Faculty,
    (faculty) => faculty.campus,
  )
  faculties: Faculty[];
}
    