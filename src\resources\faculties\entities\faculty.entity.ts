import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DevelopmentPlan } from 'src/resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import { ManyToOne, JoinColumn } from 'typeorm';
import { Campus } from 'src/resources/campus/entities/campus.entity';
import { Department } from 'src/resources/department/entities/department.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';

@Entity('HRD_T_FACULTIES', { comment: 'ตารางส่วนงาน' })
export class Faculty { 
  @Column({ name: 'FAC_ID', comment: 'รหัสคณะ' , primary: true , nullable: false, length: 10 })
  id: string;

  @Column({ name: 'FAC_NAMETH', nullable: false, comment: 'ชื่อส่วนงาน' })
  name: string;
  
  @ManyToOne(() => Campus, (campus) => campus.faculties, { nullable: false })
  @JoinColumn({ name: 'CAP_ID' })
  campus: Campus;

  @OneToMany(() => Department, (department) => department.faculty)
  departments: Department[];

  @OneToMany(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.faculty,
  )
  developmentPlans: DevelopmentPlan[];

  @OneToMany(() => CareerRecords, (careerRecords) => careerRecords.faculty)
  careerRecords: CareerRecords[];
}
