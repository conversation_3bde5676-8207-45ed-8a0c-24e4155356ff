import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

export class CareersQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Search term for career name',
    example: 'อาจารย์',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by career type (วิชาการ/สนับสนุน)',
    enum: ['วิชาการ', 'สนับสนุน'],
    required: false,
  })
  @IsString()
  @IsIn(['วิชาการ', 'สนับสนุน'])
  @IsOptional()
  career_type?: string;

  @ApiPropertyOptional({
    description: 'Filter by career name',
    example: 'อาจารย์',
    required: false,
  })
  @IsString()
  @IsOptional()
  career_name?: string;

  @ApiPropertyOptional({
    description: 'Filter by career rank',
    example: 'ผู้ช่วยศาสตราจารย์',
    required: false,
  })
  @IsString()
  @IsOptional()
  career_rank?: string;
}
