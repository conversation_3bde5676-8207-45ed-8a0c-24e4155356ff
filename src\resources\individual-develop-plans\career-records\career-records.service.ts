import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CareerRecords } from './entites/career-records.entity';
import { Repository } from 'typeorm';

@Injectable()
export class CareersRecordsService {
   constructor(
    @InjectRepository(CareerRecords)
    private readonly careerRecordsRepo: Repository<CareerRecords>,
    ) {}
  findAll(): Promise<CareerRecords[]> {
    return this.careerRecordsRepo.find();
  }


  // Example method to get a career record by id
  findOne(id: number): string {
    return `Career Record ${id}`;
  }

  // Example method to create a career record
  create(record: string): string {
    return `Created career record: ${record}`;
  }

  // Example method to update a career record
  update(id: number, record: string): string {
    return `Updated career record ${id} to ${record}`;
  }

  // Example method to delete a career record
  remove(id: number): string {
    return `Removed career record ${id}`;
  }
}