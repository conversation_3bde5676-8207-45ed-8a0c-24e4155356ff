import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseBoolPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DevelopmentPlansService } from './development-plans.service';
import { CreateDevelopmentPlanDto } from './dto/create-development-plan.dto';
import { UpdateDevelopmentPlanDto } from './dto/update-development-plan.dto';
import { DevelopmentPlansQueryDto } from './dto/development-plans-query.dto';
import { DevelopmentPlanResponseDto } from './dto/development-plans-response.dto';
import { PersonalPlansQueryDto } from './dto/personal-plans-query.dto';
import type { DataParams, DataResponse } from 'src/types/params';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';
import { UsersService } from 'src/resources/users/users.service';
import { PersonalPlanItemDto } from './dto/personal-plans-response.dto';

@ApiTags('Development Plans')
@ApiBearerAuth()
@Controller('development-plans')
export class DevelopmentPlansController {
  constructor(
    private readonly developmentPlansService: DevelopmentPlansService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new development plan' })
  @ApiResponse({
    status: 201,
    description: 'The development plan has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  create(@Body() createDevelopmentPlanDto: CreateDevelopmentPlanDto) {
    return this.developmentPlansService.create(createDevelopmentPlanDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all development plans with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of development plans',
    type: DevelopmentPlanResponseDto,
  })
  async findAll(
    @Query() query: DevelopmentPlansQueryDto,
  ): Promise<DevelopmentPlanResponseDto> {
    console.log('Raw query params:', query);
    const [data, total] = await this.developmentPlansService.findAll(query);
    return new DevelopmentPlanResponseDto(data, total, query.page, query.limit);
  }

  @Get('personal-plans')
  @RequirePermissions('user_read')
  @ApiOperation({
    summary: 'Get personal development plans with filters',
    description:
      'Retrieve personal development plans with pagination and filtering by career, faculty, and role',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getPersonalPlans(
    @Query() query: PersonalPlansQueryDto,
  ): Promise<DataResponse<PersonalPlanItemDto>> {
    const params = {
      page: query.page,
      limit: query.limit,
      search: query.search,
      sortBy: query.sortBy,
      order: query.order,
    };

    return this.usersService.getPersonalPlan(
      params,
      query.careerFilter,
      query.facultyFilter,
      query.roleFilter,
      query.careerTypeFilter,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get development plan by ID' })
  @ApiResponse({ status: 200, description: 'Returns the development plan' })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  findOne(
    @Param('id') id: string,
    @Query('isCentral', ParseBoolPipe) isCentral: boolean,
  ) {
    console.log(isCentral);
    return this.developmentPlansService.findOne(+id, isCentral);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a development plan' })
  @ApiResponse({
    status: 200,
    description: 'The development plan has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  update(
    @Param('id') id: string,
    @Body() updateDevelopmentPlanDto: UpdateDevelopmentPlanDto,
  ) {
    return this.developmentPlansService.update(+id, updateDevelopmentPlanDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a development plan' })
  @ApiResponse({
    status: 200,
    description: 'The development plan has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  remove(@Param('id') id: string) {
    return this.developmentPlansService.remove(+id);
  }
}
