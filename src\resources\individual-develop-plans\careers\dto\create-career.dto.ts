import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateCareerDto {
  @ApiProperty({
    description: 'ประเภทอาชีพ (วิชาการ/สนับสนุน)',
    example: 'วิชาการ',
  })
  @IsString()
  @IsNotEmpty()
  career_type: string;

  @ApiProperty({
    description: 'ชื่ออาชีพ (อาจารย์/นักบัญชี)',
    example: 'อาจารย์',
  })
  @IsString()
  @IsNotEmpty()
  career_name: string;

  @ApiProperty({
    description: 'ระดับตำแหน่ง (ผู้ช่วยศาสตราจารย์/ชำนาญการ)',
    example: 'ผู้ช่วยศาสตราจารย์',
  })
  @IsString()
  @IsOptional()
  career_rank?: string;
}
