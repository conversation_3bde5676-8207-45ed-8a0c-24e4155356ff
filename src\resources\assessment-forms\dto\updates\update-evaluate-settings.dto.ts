import { IsOptional, IsBoolean, Is<PERSON><PERSON>ber, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateEvaluateSettingsDto {
  @ApiProperty({
    description: 'วันที่เริ่มต้นการประเมิน',
    required: false,
    type: 'string',
    format: 'date-time',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString({}, { message: 'startAt must be a valid date string' })
  @Transform(({ value }) => {
    if (value === null || value === '' || value === undefined) {
      return undefined;
    }
    return value;
  })
  startAt?: string;

  @ApiProperty({
    description: 'วันที่สิ้นสุดการประเมิน',
    required: false,
    type: 'string',
    format: 'date-time',
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDateString({}, { message: 'endAt must be a valid date string' })
  @Transform(({ value }) => {
    if (value === null || value === '' || value === undefined) {
      return undefined;
    }
    return value;
  })
  endAt?: string;

  @ApiProperty({
    description: 'อนุญาตให้แก้ไขคำตอบได้หรือไม่',
    required: false,
    type: 'boolean',
    example: true
  })
  @IsOptional()
  @IsBoolean({ message: 'responseEdit must be a boolean value' })
  @Transform(({ value }) => {
    if (value === null || value === undefined || value === '') {
      return undefined;
    }
    return Boolean(value);
  })
  responseEdit?: boolean;

  @ApiProperty({
    description: 'จำนวนครั้งที่จำกัดการส่งคำตอบ (-1 หมายถึงไม่จำกัด)',
    required: false,
    type: 'number',
    example: 1
  })
  @IsOptional()
  @IsNumber({}, { message: 'submitLimit must be a number' })
  @Transform(({ value }) => {
    if (value === null || value === undefined || value === '') {
      return undefined;
    }
    return Number(value);
  })
  submitLimit?: number;

  @ApiProperty({
    description: 'กำหนดให้แบบประเมินนี้เป็นต้นแบบสำหรับการคัดลอก',
    required: false,
    type: 'boolean',
    example: false
  })
  @IsOptional()
  @IsBoolean({ message: 'isPrototype must be a boolean value' })
  @Transform(({ value }) => {
    if (value === null || value === undefined || value === '') {
      return undefined;
    }
    return Boolean(value);
  })
  isPrototype?: boolean;
}
