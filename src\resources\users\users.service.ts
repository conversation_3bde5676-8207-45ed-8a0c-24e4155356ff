import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like, Any, FindManyOptions } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserRolesDto,
} from './dto/update-user.dto';
import { Role } from 'src/resources/roles/entities/role.entity';
import { Faculty } from 'src/resources/faculties/entities/faculty.entity';
import { Department } from 'src/resources/department/entities/department.entity';
import { Career } from 'src/resources/individual-develop-plans/careers/entities/careers.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';
import * as bcrypt from 'bcrypt';
import { instanceTo<PERSON>lain } from 'class-transformer';
import { Data<PERSON>arams, DataResponse, createPaginatedResponse } from 'src/types';
import { FacDepUserRoles } from 'src/resources/faculties/entities/faculty-deparment-user-role.entity';
const SALT_ROUNDS = 10; // For bcrypt

export interface UserListItem {
  id: string;
  firstName: string;
  lastName: string;
  faculties: {
    id: number;
    faculty: any;
    roles: any[];
  }[];
}

export interface FacultyWithUserStatus {
  id: string;
  nameTh: string;
  nameEn: string;
  isUserInFaculty: boolean;
}

export interface FacultyUserItem {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  facultyId: string;
  facultyName: string;
  roles: {
    id: number;
    name: string;
    description: string;
  }[];
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Faculty)
    private readonly facultyRepository: Repository<Faculty>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(FacDepUserRoles)
    private readonly facDepUserRolesRepository: Repository<FacDepUserRoles>,
    @InjectRepository(Career)
    private readonly careerRepository: Repository<Career>,
    @InjectRepository(CareerRecords)
    private readonly careerRecordsRepository: Repository<CareerRecords>,
  ) {}
  async findAll(
    pag: DataParams,
    facultyFilter?: string,
    roleFilter?: number,
  ): Promise<DataResponse<UserListItem>> {
    // Build where conditions
    let whereConditions: FindManyOptions<User>['where'] = {};

    if (pag.search) {
      whereConditions = [
        { firstNameTh: Like(`%${pag.search}%`) },
        { lastNameTh: Like(`%${pag.search}%`) },
      ];
    }

    // For faculty and role filters, we'll filter after getting the data
    // This is simpler and more maintainable
    const [users, total] = await this.userRepository.findAndCount({
      relations: {
        facDepUserRoles: {
          department: {
            faculty: true,
          },
          role: true,
        },
      },
      where: pag.search ? whereConditions : {},
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (Number(pag.page) - 1) * Number(pag.limit),
      take: Number(pag.limit),
    });

    // Filter users based on faculty and role after fetching
    let filteredUsers = users;

    if (facultyFilter || roleFilter) {
      filteredUsers = users.filter((user) => {
        if (!user.facDepUserRoles || user.facDepUserRoles.length === 0) {
          return false;
        }

        // Check faculty filter
        if (facultyFilter) {
          const hasMatchingFaculty = user.facDepUserRoles.some(
            (fdr) => fdr.department?.faculty?.id === facultyFilter,
          );
          if (!hasMatchingFaculty) return false;
        }

        // Check role filter
        if (roleFilter) {
          const hasMatchingRole = user.facDepUserRoles.some((fdr) => 
            fdr.role?.id === roleFilter
          );
          if (!hasMatchingRole) return false;
        }

        return true;
      });
    }

    return createPaginatedResponse(
      filteredUsers.map((user: any) => {
        return {
          id: user.id,
          firstName: user.firstNameTh,
          lastName: user.lastNameTh,
          faculties: user.facDepUserRoles?.reduce((acc: any[], fdr: any) => {
            // Group by faculty if department exists
            if (fdr.department?.faculty) {
              const existingFaculty = acc.find(f => f.id === fdr.department.faculty.id);
              if (existingFaculty) {
                existingFaculty.roles.push(fdr.role);
              } else {
                acc.push({
                  id: fdr.department.faculty.id,
                  faculty: fdr.department.faculty,
                  roles: [fdr.role],
                });
              }
            }
            return acc;
          }, []) || [],
        };
      }),
      filteredUsers.length, // Use filtered count
      Number(pag.page),
      Number(pag.limit),
    );
  }

  async findOne(
    id: string,
    loadRelations: any = {
      facDepUserRoles: {
        department: {
          faculty: true,
        },
        role: true,
      },
    },
  ): Promise<UserListItem> {
    console.log('GET FIND ONE');

    const user = await this.userRepository.findOne({
      where: { id },
      relations: loadRelations,
    });

    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found`);
    }

    // Extract all roles from faculty department user roles
    const allRoles = user.facDepUserRoles?.map((fdr: any) => fdr.role) || [];

    // Map the user data to the same structure as in findAll
    const mappedUser = {
      id: user.id,
      firstName: user.firstNameTh,
      lastName: user.lastNameTh,
      faculties: user.facDepUserRoles?.reduce((acc: any[], fdr: any) => {
        // Group by faculty if department exists
        if (fdr.department?.faculty) {
          const existingFaculty = acc.find(f => f.id === fdr.department.faculty.id);
          if (existingFaculty) {
            existingFaculty.roles.push(fdr.role);
          } else {
            acc.push({
              id: fdr.department.faculty.id,
              faculty: fdr.department.faculty,
              roles: [fdr.role],
            });
          }
        }
        return acc;
      }, []) || [],
    };

    console.log('USER RETURN', mappedUser);

    return mappedUser;
  }

  async findOneByEmail(
    email: string,
    loadRelations: any = {
      facDepUserRoles: {
        department: {
          faculty: true,
        },
        role: true,
      },
    },
  ): Promise<User | null> {
    // This method might be used internally for auth, so it returns the full User object including password
    return this.userRepository.findOne({
      where: { email },
      relations: loadRelations,
    });
  }

  async remove(id: string): Promise<void> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }
    await this.userRepository.remove(user);
  }

  async getFacultiesWithUserStatus(
    userId: string,
    pag: DataParams,
  ): Promise<DataResponse<FacultyWithUserStatus>> {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    const [allFaculties, total] = await this.facultyRepository.findAndCount({
      where: pag.search
        ? [
            { name: Like(`%${pag.search}%`) },
          ]
        : {},
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
    });

    const userFacDepRoles = await this.facDepUserRolesRepository.find({
      where: { personId: userId },
      relations: ['department', 'department.faculty'],
    });
    const userFacultyIds = userFacDepRoles
      .filter(fdr => fdr.department?.faculty)
      .map(fdr => fdr.department.faculty.id);
    
    const data = allFaculties.map((faculty) => ({
      id: faculty.id,
      nameTh: faculty.name,
      nameEn: faculty.name,
      isUserInFaculty: userFacultyIds.includes(faculty.id),
    }));

    return createPaginatedResponse(data, total, pag.page, pag.limit);
  }

  async updateUserFaculties(
    userId: string,
    departmentIds: string[],
  ): Promise<{
    message: string;
    addedDepartments: Department[];
    removedDepartments: Department[];
  }> {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (departmentIds.length > 0) {
      const departments = await this.departmentRepository.findBy({
        id: In(departmentIds),
      });
      if (departments.length !== departmentIds.length) {
        const foundDepartmentIds = departments.map((d) => d.id);
        const notFoundIds = departmentIds.filter(
          (id) => !foundDepartmentIds.includes(id),
        );
        throw new BadRequestException(
          `One or more departments not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
    }
    const currentFacDepRoles = await this.facDepUserRolesRepository.find({
      where: { personId: userId },
      relations: ['department'],
    });
    const currentDepartmentIds = currentFacDepRoles
      .filter(fdr => fdr.department)
      .map((fdr) => fdr.departmentId);
    
    const departmentsToAdd = departmentIds.filter(
      (id) => !currentDepartmentIds.includes(id),
    );
    const departmentsToRemove = currentDepartmentIds.filter(
      (id) => !departmentIds.includes(id),
    );
    
    if (departmentsToRemove.length > 0) {
      await this.facDepUserRolesRepository.delete({
        personId: userId,
        departmentId: In(departmentsToRemove),
      });
    }
    if (departmentsToAdd.length > 0) {
      const newFacDepRoles = departmentsToAdd.map((departmentId) =>
        this.facDepUserRolesRepository.create({ 
          personId: userId, 
          departmentId,
          roleId: 1, // Default role, you may need to adjust this
          isDepartmentDirector: false
        }),
      );
      await this.facDepUserRolesRepository.save(newFacDepRoles);
    }
    const addedDepartments =
      departmentsToAdd.length > 0
        ? await this.departmentRepository.findBy({ id: In(departmentsToAdd) })
        : [];
    const removedDepartments =
      departmentsToRemove.length > 0
        ? await this.departmentRepository.findBy({ id: In(departmentsToRemove) })
        : [];

    return {
      message: 'User departments updated successfully',
      addedDepartments,
      removedDepartments,
    };
  }

  async updateRoles(
    userId: string,
    roleIds: number[],
    departmentId?: string,
  ): Promise<{
    message: string;
    addedRoles: Role[];
    removedRoles: Role[];
  }> {
    // Implementation for updating user roles
    console.log('RolesId', roleIds);
    console.log('DepartmentId', departmentId);

    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (roleIds.length > 0) {
      const roles = await this.roleRepository.findBy({ id: In(roleIds) });
      console.log('roles', roles);
      if (roles.length !== roleIds.length) {
        const foundRoleIds = roles.map((r) => r.id);
        const notFoundIds = roleIds.filter((id) => !foundRoleIds.includes(id));
        throw new BadRequestException(
          `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
    }
    
    const currentRoles = await this.facDepUserRolesRepository.find({
      where: { 
        personId: userId,
        ...(departmentId && { departmentId })
      },
      relations: ['role'],
    });
    console.log('currentRoles', currentRoles);

    const currentRoleIds = currentRoles.map((r) => r.roleId);
    const rolesToAdd = roleIds.filter((id) => !currentRoleIds.includes(id));
    console.log('rolesToAdd', rolesToAdd);
    const rolesToRemove = currentRoleIds.filter((id) => !roleIds.includes(id));
    console.log('rolesToRemove', rolesToRemove);

    if (rolesToAdd.length > 0) {
      const newUserRoles = rolesToAdd.map((roleId) =>
        this.facDepUserRolesRepository.create({
          personId: userId,
          departmentId: departmentId || null,
          roleId: roleId,
          isDepartmentDirector: false
        }),
      );
      await this.facDepUserRolesRepository.save(newUserRoles);
    }

    if (rolesToRemove.length > 0) {
      await this.facDepUserRolesRepository.delete({
        personId: userId,
        roleId: In(rolesToRemove),
        ...(departmentId && { departmentId })
      });
    }

    const addedRoles =
      rolesToAdd.length > 0
        ? await this.roleRepository.findBy({ id: In(rolesToAdd) })
        : [];
    const removedRoles =
      rolesToRemove.length > 0
        ? await this.roleRepository.findBy({ id: In(rolesToRemove) })
        : [];

    return {
      message: 'User roles updated successfully',
      addedRoles,
      removedRoles,
    };
  }

  async getUserFacultyUsers(
    userId: string,
    pag: DataParams,
  ): Promise<DataResponse<FacultyUserItem>> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException(`User with ID "${userId}" not found.`);
      }

      // Find faculty department user roles associated with this user
      const [facDepUserRoles, total] =
        await this.facDepUserRolesRepository.findAndCount({
          where: { personId: userId },
          relations: {
            department: {
              faculty: true,
            },
            user: true,
            role: true,
          },
          order: {
            [pag.sortBy || 'id']: pag.order || 'ASC',
          },
          skip: (pag.page - 1) * pag.limit,
          take: pag.limit,
        });

      // Transform the data to return only relevant fields
      const data = facDepUserRoles.map((facDepRole) => {
        const roles = facDepRole.role ? [{
          id: facDepRole.role.id,
          name: facDepRole.role.name,
          description: facDepRole.role.description,
        }] : [];

        return {
          id: facDepRole.user.id,
          userId: facDepRole.user.id,
          name: `${facDepRole.user.firstNameTh} ${facDepRole.user.lastNameTh}`.trim(),
          email: facDepRole.user.email,
          firstName: facDepRole.user.firstNameTh,
          lastName: facDepRole.user.lastNameTh,
          code: facDepRole.user.username,
          facultyId: facDepRole.department?.faculty?.id || '',
          facultyName: facDepRole.department?.faculty?.name || '', // Using Thai name as default
          roles,
        };
      });

      return createPaginatedResponse(data, total, pag.page, pag.limit);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve faculty users: ${error.message}`,
      );
    }
  }

  async getPersonalPlan(
    pag: DataParams,
    careerFilter?: string,
    facultyFilter?: number,
    roleFilter?: number,
    careerTypeFilter?: string,
  ): Promise<DataResponse<{ id: number; name: string; careerName: string }>> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.facultyUsers', 'fu')
      .leftJoin('fu.faculty', 'faculty')
      .leftJoin('fu.facultyUserRoles', 'fur')
      .leftJoin('fur.role', 'role')
      .leftJoin('user.careerRecords', 'cr')
      .leftJoin(Career, 'career', 'career.id = cr.career_Id')
      .select([
        'user.id',
        'user.firstNameTh',
        'user.lastNameTh',
        'career.career_name',
      ])
      .distinct(true);

    this.applyFilters(queryBuilder, {
      careerFilter,
      facultyFilter,
      roleFilter,
      careerTypeFilter,
    });
    this.applySearchAndSort(queryBuilder, pag);

    const [total, results] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder
        .skip((pag.page - 1) * pag.limit)
        .take(pag.limit)
        .getRawMany(),
    ]);

    const data = results.map((result) => ({
      id: result.user_PSN_ID,
      name: `${result.user_PSN_FNAMETH} ${result.user_PSN_LNAMETH}`,
      careerName: result.career_CR_CAREER_NAME || 'ไม่ระบุ',
    }));

    return {
      data,
      total,
      currentPage: pag.page,
      itemsPerPage: pag.limit,
      totalPages: Math.ceil(total / pag.limit),
      hasPrev: pag.page > 1,
      hasNext: pag.page * pag.limit < total,
    };
  }

  private applyFilters(
    queryBuilder: any,
    filters: {
      careerFilter?: string;
      facultyFilter?: number;
      roleFilter?: number;
      careerTypeFilter?: string;
    },
  ) {
    const { careerFilter, facultyFilter, roleFilter, careerTypeFilter } =
      filters;

    if (careerFilter) {
      queryBuilder.andWhere('career.career_name LIKE :careerName', {
        careerName: `%${careerFilter}%`,
      });
    }
    if (facultyFilter) {
      queryBuilder.andWhere('faculty.id = :facultyId', {
        facultyId: facultyFilter,
      });
    }
    if (roleFilter) {
      queryBuilder.andWhere('role.id = :roleId', { roleId: roleFilter });
    }
    if (careerTypeFilter) {
      queryBuilder.andWhere('career.career_type LIKE :careerType', {
        careerType: `%${careerTypeFilter}%`,
      });
    }
  }

  private applySearchAndSort(queryBuilder: any, pag: DataParams) {
    if (pag.search) {
      queryBuilder.andWhere(
        '(user.firstNameTh LIKE :search OR user.lastNameTh LIKE :search OR career.career_name LIKE :search)',
        { search: `%${pag.search}%` },
      );
    }

    const sortFields = {
      id: 'user.id',
      firstName: 'user.firstNameTh',
      lastName: 'user.lastNameTh',
      careerName: 'career.career_name',
    };

    const sortField = sortFields[pag.sortBy] || 'user.id';
    queryBuilder.orderBy(sortField, pag.order || 'ASC');
  }
}
