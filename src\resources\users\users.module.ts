import { Modu<PERSON> } from '@nestjs/common';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from '../roles/entities/role.entity';
import { User } from './entities/user.entity';
import { UsersService } from './users.service';
import { Faculty } from 'src/resources/faculties/entities/faculty.entity';
import { FacDepUserRoles } from 'src/resources/faculties/entities/faculty-deparment-user-role.entity';
import { Department } from 'src/resources/department/entities/department.entity';
import { Career } from 'src/resources/individual-develop-plans/careers/entities/careers.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Role,
      Faculty,
      Department,
      FacDepUserRoles,
      Career,
      CareerRecords,
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService, UsersModule],
})
export class UsersModule {}
