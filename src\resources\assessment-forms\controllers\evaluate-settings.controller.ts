import {
  <PERSON>,
  <PERSON>,
  Param,
  Body,
  ParseIntPipe,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { UpdateEvaluateSettingsDto } from '../dto/updates/update-evaluate-settings.dto';
import { Assessment } from '../entities/assessment.entity';
import { AssessmentsService } from '../services/assessments.service';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';

@ApiTags('Evaluate Settings')
@ApiBearerAuth()
@Controller('assessments/:id/evaluate-settings')
export class EvaluateSettingsController {
  constructor(private readonly assessmentsService: AssessmentsService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get evaluate settings',
    description: 'ดึงการตั้งค่าของแบบประเมิน'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'รหัสของแบบประเมิน', 
    type: Number 
  })
  @ApiResponse({
    status: 200,
    description: 'ข้อมูลการตั้งค่าแบบประเมิน',
    type: Assessment,
  })
  @RequirePermissions('manage_own_surveys')
  async getEvaluateSettings(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Partial<Assessment>> {
    const { assessment } = await this.assessmentsService.findOne(id, { page: 1, limit: 1 });
    
    // Return only settings-related fields
    return {
      id: assessment.id,
      name: assessment.name,
      startAt: assessment.startAt,
      endAt: assessment.endAt,
      responseEdit: assessment.responseEdit,
      submitLimit: assessment.submitLimit,
      isPrototype: assessment.isPrototype,
      type: assessment.type,
    };
  }

  @Patch()
  @ApiOperation({ 
    summary: 'Update evaluate settings',
    description: 'อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'รหัสของแบบประเมิน', 
    type: Number 
  })
  @ApiResponse({
    status: 200,
    description: 'การตั้งค่าแบบประเมินถูกอัปเดตเรียบร้อยแล้ว',
    type: Assessment,
  })
  @RequirePermissions('manage_own_surveys')
  async updateEvaluateSettings(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEvaluateSettingsDto: UpdateEvaluateSettingsDto,
  ): Promise<Assessment> {
    // Cast to UpdateAssessmentDto for compatibility with existing service
    const settingsUpdate = updateEvaluateSettingsDto as any;

    return this.assessmentsService.update(id, settingsUpdate);
  }
}
