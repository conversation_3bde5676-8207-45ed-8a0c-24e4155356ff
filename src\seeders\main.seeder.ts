import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import { Permission } from '../resources/permissions/entities/permission.entity';
import { Role } from '../resources/roles/entities/role.entity';
import { User } from '../resources/users/entities/user.entity';
import { DevelopmentPlan } from '../resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import * as bcrypt from 'bcrypt';
import * as fs from 'fs';
import * as path from 'path';

export class MainSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<any> {
    console.log('🌱 Starting database seeding...');

    // Seed permissions first
    await this.seedPermissions(dataSource);
    
    // Seed roles and assign permissions
    await this.seedRoles(dataSource);
    
    // Seed users
    await this.seedUsers(dataSource);

    // Seed development plans
    await this.seedDevelopmentPlans(dataSource);

    console.log('✅ Database seeding completed successfully!');
  }

  private loadFixture<T>(fixtureName: string): T[] {
    const fixturePath = path.join(__dirname, 'fixtures', `${fixtureName}.json`);
    const fixtureData = fs.readFileSync(fixturePath, 'utf-8');
    return JSON.parse(fixtureData);
  }

  private async seedPermissions(dataSource: DataSource) {
    console.log('📋 Seeding permissions...');
    const permissionRepo = dataSource.getRepository(Permission);
    const permissions = this.loadFixture<any>('permissions');

    for (const permissionData of permissions) {
      const existingPermission = await permissionRepo.findOne({
        where: { name: permissionData.name }
      });

      if (!existingPermission) {
        await permissionRepo.save(permissionRepo.create(permissionData));
        console.log(`  ✨ Created permission: ${permissionData.name}`);
      } else {
        console.log(`  📋 Permission already exists: ${permissionData.name}`);
      }
    }
  }

  private async seedRoles(dataSource: DataSource) {
    console.log('👥 Seeding roles...');
    const roleRepo = dataSource.getRepository(Role);
    const permissionRepo = dataSource.getRepository(Permission);
    const rolesData = this.loadFixture<any>('roles');

    for (const roleData of rolesData) {
      // Create or find the role
      let role = await roleRepo.findOne({
        where: { name: roleData.name },
        relations: ['permissions']
      });

      if (!role) {
        role = roleRepo.create({
          name: roleData.name,
          description: roleData.description,
          department: roleData.department,
        });
        await roleRepo.save(role);
        console.log(`  ✨ Created role: ${roleData.name}`);
      } else {
        console.log(`  📋 Role already exists: ${roleData.name}`);
      }

      // Assign permissions to the role
      for (const permissionName of roleData.permissions) {
        const permission = await permissionRepo.findOne({
          where: { name: permissionName }
        });

        if (permission) {
          // Check if permission is already assigned
          const existingAssignment = await dataSource.query(`
            SELECT * FROM HRD_T_ROLES_HAS_PERMISSIONS 
            WHERE ROL_ID = ? AND PER_ID = ?
          `, [role.id, permission.id]);

          if (!existingAssignment.length) {
            await dataSource.query(`
              INSERT INTO HRD_T_ROLES_HAS_PERMISSIONS (ROL_ID, PER_ID)
              VALUES (?, ?)
            `, [role.id, permission.id]);
            console.log(`    🔗 Assigned permission "${permissionName}" to role "${roleData.name}"`);
          }
        } else {
          console.warn(`    ⚠️ Permission "${permissionName}" not found for role "${roleData.name}"`);
        }
      }
    }
  }

  private async seedUsers(dataSource: DataSource) {
    console.log('� Seeding users...');
    const userRepo = dataSource.getRepository(User);
    const roleRepo = dataSource.getRepository(Role);
    const usersData = this.loadFixture<any>('users');

    for (const userData of usersData) {
      // Check if user already exists (by email or id)
      const existingUser = await userRepo.findOne({
        where: [
          { email: userData.email },
          { id: userData.id || userData.email.split('@')[0] }
        ]
      });

      if (existingUser) {
        console.log(`  📋 User already exists with email: ${userData.email}`);
        continue;
      }

      // Get default password from environment or use default
      const defaultPassword = userData.isSystem 
        ? (process.env.SUPERADMIN_PASSWORD || '1234')
        : (process.env.DEFAULT_USER_PASSWORD || 'password123');

      // Hash the password
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);

      // Create user
      const user = userRepo.create({
        id: userData.id || userData.email.split('@')[0], // Use provided ID or derive from email
        idcard: userData.idcard || '1234567890123',
        prefixTh: userData.prefixTh || 'นาย',
        prefixEn: userData.prefixEn || 'Mr.',
        firstNameTh: userData.firstNameTh || userData.name?.split(' ')[0] || 'ชื่อ',
        firstNameEn: userData.firstNameEn || userData.name?.split(' ')[0] || 'FirstName',
        lastNameTh: userData.lastNameTh || userData.name?.split(' ')[1] || 'นามสกุล',
        lastNameEn: userData.lastNameEn || userData.name?.split(' ')[1] || 'LastName',
        campusName: userData.campusName || 'มหาวิทยาลัยบูรพา',
        facultyName: userData.facultyName || 'คณะเทคโนโลยีสารสนเทศ',
        departmentName: userData.departmentName || 'ภาควิชาวิทยาการคอมพิวเตอร์',
        groupName: userData.groupName || 'กลุ่มวิชาการ',
        positionName: userData.positionName || 'อาจารย์',
        positionLevel: userData.positionLevel || 'ปฏิบัติการ',
        startDate: userData.startDate ? new Date(userData.startDate) : new Date(),
        workStatus: userData.workStatus || 'N',
        email: userData.email,
        username: userData.username || userData.email.split('@')[0],
        password: hashedPassword,
      });

      await userRepo.save(user);
      console.log(`  ✨ Created user: ${userData.email}`);

      // TODO: Role assignment through FacDepUserRoles entity
      // Note: Roles are assigned through faculty-department-user-role relationships
      // This would require creating departments and faculty relationships first
      console.log(`    � Note: Role assignment for "${userData.roles?.join(', ')}" needs to be implemented through FacDepUserRoles`);

      // Show credentials for system users
      if (userData.isSystem) {
        console.log('🔑 System user credentials:');
        console.log(`   Email: ${userData.email}`);
        console.log(`   Password: ${process.env.NODE_ENV === 'production' ? '[Check SUPERADMIN_PASSWORD env var]' : defaultPassword}`);
      }
    }
  }

  private async seedDevelopmentPlans(dataSource: DataSource) {
    console.log('📚 Seeding development plans...');
    const developmentPlanRepo = dataSource.getRepository(DevelopmentPlan);
    const developmentPlansData = this.loadFixture<any>('development-plans');

    for (const planData of developmentPlansData) {
      const existingPlan = await developmentPlanRepo.findOne({
        where: { name: planData.name }
      });

      if (!existingPlan) {
        await developmentPlanRepo.save(developmentPlanRepo.create(planData));
        console.log(`  ✨ Created development plan: ${planData.name}`);
      } else {
        console.log(`  📋 Development plan already exists: ${planData.name}`);
      }
    }
  }
}
