import { PartialType } from '@nestjs/mapped-types';
import { CreatePermissionDto } from './create-permission.dto';
import { IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdatePermissionDto extends PartialType(CreatePermissionDto) {}

export class SetPermissionStatusDto {
  @ApiProperty({
    description: 'สถานะการใช้งานสิทธิ์',
    example: true,
    type: Boolean,
  })
  @IsBoolean()
  status: boolean;
}
