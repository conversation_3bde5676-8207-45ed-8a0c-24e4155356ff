import { PartialType } from '@nestjs/mapped-types';
import { CreateRoleDto } from './create-role.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class UpdateRoleDto extends PartialType(CreateRoleDto) {}
export class UpdateFacultyUsersRoleDto {
    
    @ApiProperty({
      description: 'Array of user IDs to assign to the role',
      example: [1, 2, 3],
      type: [Number],
    })
    @IsArray()
    @IsNumber({}, { each: true })
    @IsNotEmpty()
    userIds: number[];
  
    @ApiProperty({
      description: 'Faculty user ID',
      example: 1,
    })
    @IsNumber()
    @IsNotEmpty()
    facultyId: number;
  }