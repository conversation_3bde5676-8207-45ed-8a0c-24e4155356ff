import {
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Optional,
  IsPositive,
  IsString,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateOptionDto {
  @ApiProperty({
    description: 'The text content of the option',
    type: String,
    maxLength: 255,
    example: 'Paris',
  })
  @IsString()
  @MaxLength(255)
  optionText?: string;

  @ApiProperty({
    description: 'Path to the image associated with the option',
    type: String,
    maxLength: 255,
    format: 'binary',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  imagePath?: string;

  @ApiProperty({
    description: 'The score value assigned to this option (for scoring)',
    type: Number,
    required: false,
    minimum: 1,
    example: 1,
    default: 1,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  value?: number;

  @ApiProperty({
    description:
      'ID of the next section to navigate to if this option is selected (for branching logic)',
    type: Number,
    required: false,
    default: null,
  })
  @IsOptional()
  @IsNumber()
  nextSection?: number;

  @ApiProperty({
    description: 'The order/position of this option in the assessment',
    type: Number,
    minimum: 1,
    example: 1,
  })
  @Type(() => Number)
  @IsOptional(  )
  sequence?: number;

  @ApiProperty({
    description: 'ID of the item block this option belongs to',
    type: Number,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  itemBlockId?: number;

  // ✅ ส่วนนี้สำคัญ! เพื่อให้ Swagger รู้ว่านี่คือไฟล์
  // @ApiProperty({
  //   type: 'string',
  //   format: 'binary',
  //   description: 'ไฟล์รูปภาพของตัวเลือก',
  //   required: false,
  // })
  // file?: any; // ใช้ any เพราะ Nest ไม่รองรับ Express.Multer.File ใน swagger
}
