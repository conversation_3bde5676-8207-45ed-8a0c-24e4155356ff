import { IsDate, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSubmissionDto {
  @ApiProperty({
    description: 'The date and time when the user started the assessment',
    type: Date,
    example: '2023-01-15T10:30:00Z',
  })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  startAt: Date;

  @ApiProperty({
    description: 'The date and time when the user completed the assessment',
    type: Date,
    example: '2023-01-15T11:45:00Z',
  })
  @IsOptional() // ✅ อนุญาตให้เป็น undefined หรือ null
  @Type(() => Date)
  @IsDate()
  endAt: Date | null;

  @ApiProperty({
    description: 'ID of the user who submitted the assessment',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'ID of the assessment being submitted',
    type: Number,
  })
  @IsNotEmpty()
  @IsNumber()
  assessmentId: number;
}
