/**
 * Generic interface for paginated data responses
 * Provides consistent structure for all paginated API responses
 */
export interface DataTablePaginate<T = any> {
  /** Array of data items */
  data: T[];
  /** Total number of items available */
  total: number;
  /** Current page number */
  currentPage: number;
  /** Number of items per page */
  itemsPerPage: number;
  /** Total number of pages */
  totalPages?: number;
  /** Whether there are previous pages */
  hasPrev?: boolean;
  /** Whether there are next pages */
  hasNext?: boolean;
}

/**
 * Creates a paginated response object
 * @param data Array of data items
 * @param total Total number of items
 * @param page Current page number
 * @param limit Number of items per page
 * @returns Paginated response object
 */
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): DataTablePaginate<T> {
  const totalPages = Math.ceil(total / limit);
  return {
    data,
    total,
    currentPage: page,
    itemsPerPage: limit,
    totalPages,
    hasPrev: page > 1,
    hasNext: page < totalPages,
  };
}
