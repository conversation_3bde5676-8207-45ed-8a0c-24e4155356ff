import { Injectable } from '@nestjs/common';
import { extname } from 'path';
import * as sharp from 'sharp';
import { ApiService } from 'src/api/api.service';
import { UploadFileDto } from 'src/types/api/uploadFile';

@Injectable()
export class FileUploadService {
  constructor(private readonly apiService: ApiService) {}

   async processImagePath(
      filePath: string ,
    ): Promise<string | undefined> {
      if (filePath) {
        try {
          const result = await this.apiService.getPublicFile(filePath);
          return result?.result.file_1.view ?? filePath;
        } catch (error) {
          console.error(`Error fetching file ${filePath}:`, error);
          return filePath;
        }
      }
      return filePath;
    }

  getNewFileName(originalName: string): string {
    return originalName.replace(extname(originalName), '.webp');
  }

  createWebpFile(
    file: Express.Multer.File,
    buffer: Buffer,
    newFileName: string,
  ): Express.Multer.File {
    return {
      ...file,
      buffer,
      originalname: newFileName,
      mimetype: 'image/webp',
      size: buffer.length,
      filename: newFileName,
    };
  }

  async getInputBuffer(file: Express.Multer.File): Promise<Buffer> {
    console.log('file buffer', file.buffer);
    console.log('filePath', file.path);

    if (file.buffer) return file.buffer;
    if (file.path) {
      const fs = await import('fs/promises');
      return fs.readFile(file.path);
    }

    throw new Error('File input not found!');
  }

  async uploadAndGetImagePath(
    file: Express.Multer.File,
  ): Promise<string | null> {
    const imageTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const mimeToExtension: Record<string, string> = {
      // รูปภาพ
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',

      // เอกสาร
      'application/pdf': 'pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        'docx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        'xlsx',
      'text/csv': 'csv',

      // เสียง
      'audio/mpeg': 'mp3',

      // วิดีโอ
      'video/mp4': 'mp4',
      'video/quicktime': 'mov',
      'video/x-msvideo': 'avi',
      'video/x-matroska': 'mkv',
    };

    let uploaded;

    if (imageTypes.includes(file.mimetype)) {
      const inputBuffer = await this.getInputBuffer(file);
      console.log('inputBuffer', inputBuffer);
      const webpBuffer = await sharp(inputBuffer)
        .webp({ quality: 80 })
        .toBuffer();

      const newFileName = this.getNewFileName(file.originalname);
      const webpFile = this.createWebpFile(file, webpBuffer, newFileName);

      const uploadFileDto: UploadFileDto = {
        path: '/uploaded_files',
        fileName: newFileName,
        fileType: 'webp',
      };

      uploaded = await this.apiService.uploadFile(webpFile, uploadFileDto);
    } else {
      const uploadFileDto: UploadFileDto = {
        path: '/uploaded_files',
        fileName: file.originalname,
        fileType: mimeToExtension[file.mimetype] ?? 'unknown',
      };

      console.log('Dto', uploadFileDto);
      console.log('Dto type', uploadFileDto);

      uploaded = await this.apiService.uploadFile(file, uploadFileDto);
    }

    return uploaded?.result ?? null;
  }

  async getSignedUrl(fileName: string | null): Promise<string | null> {
    if (!fileName) return null;
    try {
      const publicFile = await this.apiService.getPublicFile(fileName);
      return publicFile?.result?.file_1?.view ?? null;
    } catch (error) {
      console.error('Failed to get signed URL:', error);
      return null;
    }
  }

  async deleteFileByUrl(fileName: string | null): Promise<void> {
    if (fileName) {
      await this.apiService.deleteFile(fileName);
    }
  }
}
