import { <PERSON>NotE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class StartQuizDto {
  @ApiProperty({
    description: 'ID of the user starting the assessment',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'ID of the assessment being started',
    type: String, // Fix type to String for linkUrl
  })
  @IsNotEmpty()
  linkUrl: string;
}
