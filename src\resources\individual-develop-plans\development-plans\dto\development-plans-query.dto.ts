import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

export class DevelopmentPlansQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({ description: 'Search term for name or description' })
  @IsOptional()
  search?: string;

  @ApiProperty({
    description: 'Filter by isCentral',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isCentral?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by parent ID',
    required: false,
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  parentId?: number;
}
