import { DataSource } from 'typeorm';
import { Department } from '../../resources/department/entities/department.entity';
import { Faculty } from '../../resources/faculties/entities/faculty.entity';

export async function seedDepartments(dataSource: DataSource) {
  const departmentRepository = dataSource.getRepository(Department);
  const facultyRepository = dataSource.getRepository(Faculty);

  console.log('Starting departments seeding...');

  // Get faculty data first
  const faculties = await facultyRepository.find();
  if (faculties.length === 0) {
    console.log('No faculty data found. Please seed faculty data first.');
    return;
  }

  const departmentsData = [
  
    // กลุ่มงานในคณะวิทยาการสารสนเทศ (faculty id: 09)
    { id: '01', name: 'สำนักงานการศึกษา', facultyId: '09' },
    { id: '02', name: 'สำนักงานคณบดี', facultyId: '09' },

    // กลุ่มงานในสำนักงานอธิการบดี (faculty id: 20)
    { id: '03', name: 'ฝ่ายพัฒนาระบบ', facultyId: '20' },
    { id: '04', name: 'สำนักงานผู้อำนวยการ', facultyId: '20' },

    // กลุ่มงานในสำนักคอมพิวเตอร์ (faculty id: 21)
    { id: '05', name: 'กองบริหารและพัฒนาทรัพยากรบุคคล', facultyId: '21' },
    { id: '06', name: 'กองบริหารการศึกษา', facultyId: '21' },
    { id: '07', name: 'กองกิจการนิสิต', facultyId: '21' }

  ];

  for (const departmentData of departmentsData) {
    const existingDepartment = await departmentRepository.findOne({
      where: { id: departmentData.id }
    });

    if (!existingDepartment) {
      // Find the faculty
      const faculty = await facultyRepository.findOne({
        where: { id: departmentData.facultyId }
      });

      if (!faculty) {
        console.log(`Faculty with id ${departmentData.facultyId} not found for department ${departmentData.name}`);
        continue;
      }

      const department = departmentRepository.create({
        id: departmentData.id,
        name: departmentData.name,
        faculty: faculty
      });
      
      await departmentRepository.save(department);
      console.log(`Created department: ${departmentData.name} (${departmentData.id}) in ${faculty.name}`);
    } else {
      console.log(`Department already exists: ${departmentData.name} (${departmentData.id})`);
    }
  }

  console.log('Departments seeding completed!');
}
