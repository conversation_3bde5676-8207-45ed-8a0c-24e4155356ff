import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { AgeWork } from './age-work.entity';
import { DevelopmentPlan } from '../../development-plans/entities/development-plan.entity';

@Entity('IDP_T_AGE_WORK_CRITERIA', { comment: 'ตารางเกณฑ์อายุงาน' })
export class AgeWorkCriteria {
  @PrimaryGeneratedColumn({ name: 'ID', comment: 'รหัสเกณฑ์อายุงาน' })
  id: number;

  @Column({ name: 'NAME', type: 'varchar', length: 255, comment: 'ชื่อเกณฑ์อายุงาน' })
  name: string;

  @Column({ name: 'DP_ID', type: 'int', comment: 'รหัสแผนพัฒนาบุคลากร' })
  developmentPlanId: number;

  @OneToMany(() => AgeWork, (ageWork) => ageWork.ageWorkCriteria)
  ageWorks: AgeWork[];

  // development plan
  @OneToMany(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.ageWorkCriteria,
  )
  developmentPlans: DevelopmentPlan[];
}
