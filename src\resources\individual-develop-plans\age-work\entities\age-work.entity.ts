import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { AgeWorkCriteria } from './age-work-criteria.entity';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_T_AGE_WORK', { comment: 'ตารางช่วงอายุงาน' })
export class AgeWork {
  @PrimaryGeneratedColumn({ name: 'ID', comment: 'รหัสช่วงอายุงาน' })
  id: number;

  @Column({ name: 'NAME', type: 'varchar', length: 255, comment: 'ชื่อช่วงอายุงาน' })
  name: string;

  @Column({ name: 'START_YEAR', type: 'int', comment: 'ปีเริ่มต้น' })
  startYear: number;

  @Column({ name: 'END_YEAR', type: 'int', comment: 'ปีสิ้นสุด' })
  endYear: number;

  @Column({ name: 'AGE_WORK_CRITERIA_ID', type: 'int', comment: 'รหัสเกณฑ์อายุงาน' })
  ageWorkCriteriaId: number;

  @ManyToOne(() => AgeWorkCriteria, (criteria) => criteria.ageWorks)
  @JoinColumn({ name: 'AGE_WORK_CRITERIA_ID' })
  ageWorkCriteria: AgeWorkCriteria;

  @OneToMany(() => TypePlan, (typePlan) => typePlan.ageWork)
  typePlans: TypePlan[];
}
