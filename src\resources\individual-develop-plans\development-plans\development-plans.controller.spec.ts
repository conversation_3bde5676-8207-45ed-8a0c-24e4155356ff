import { Test, TestingModule } from '@nestjs/testing';
import { DevelopmentPlansController } from './development-plans.controller';
import { DevelopmentPlansService } from './development-plans.service';

describe('DevelopmentPlansController', () => {
  let controller: DevelopmentPlansController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DevelopmentPlansController],
      providers: [DevelopmentPlansService],
    }).compile();

    controller = module.get<DevelopmentPlansController>(DevelopmentPlansController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
