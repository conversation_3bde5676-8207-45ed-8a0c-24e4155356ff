import { Assessment } from 'src/resources/assessment-forms/entities/assessment.entity';
import { Submission } from 'src/resources/assessment-forms/entities/submission.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import {
  Entity,
  Column,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserSkill } from 'src/resources/individual-develop-plans/skills/entities/user-skill.entity';
import { PermAuditSkill } from 'src/resources/individual-develop-plans/skills/entities/perm-audit-skill.entity';
import { Skill } from 'src/resources/individual-develop-plans/skills/entities/skill.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';

import { TypePlan } from 'src/resources/individual-develop-plans/type-plans/entities/type-plan.entity';
import { FacDepUserRoles } from 'src/resources/faculties/entities/faculty-deparment-user-role.entity';

@Entity('HRD_T_USERS', {
  comment: 'ตารางผู้ใช้งานระบบ'
})
export class User {
  @Column({ name: 'PSN_ID', length: 10, primary: true, comment: 'รหัสบุคลากร' , nullable: false })
  id: string;

  @Column({ name: 'PSN_IDCARD', length: 20 , comment: 'รหัสบัตรประชาชน' })
  idcard: string;

  @Column({ name: 'PRF_NAMETH', length: 20 , comment: 'คำนำหน้าชื่อภาษาไทย' })
  prefixTh: string;

  @Column({ name: 'PRF_NAMEEN', length: 20 , comment: 'คำนำหน้าชื่อภาษาอังกฤษ' })
  prefixEn: string;

  @Column({ name: 'PSN_FNAMETH', length: 50, comment: 'ชื่อภาษาไทย' })
  firstNameTh: string;

  @Column({ name: 'PSN_FNAMEEN', length: 50, comment: 'ชื่อภาษาอังกฤษ' })
  firstNameEn: string;

  @Column({ name: 'PSN_LNAMETH', length: 50, comment: 'นามสกุลภาษาไทย' })
  lastNameTh: string;

  @Column({ name: 'PSN_LNAMEEN', length: 50, comment: 'นามสกุลภาษาอังกฤษ' })
  lastNameEn: string;

  @Column({ name: 'CAP_NAMETH', length: 255, comment: 'ชื่อวิทยาเขต' })
  campusName: string;

  @Column({ name: 'FAC_NAMETH', length: 255, comment: 'ชื่อส่วนงาน' })
  facultyName: string;

  @Column({ name: 'DEP_NAMETH', length: 255, comment: 'ชื่อฝ่าย/ภาควิชา' })
  departmentName: string;

  @Column({ name: 'GRL_NAMETH', length: 50, comment: 'ชื่อกลุ่ม/ประเภทสายงาน' })
  groupName: string;

  @Column({ name: 'LIW_NAMETH', length: 255, comment: 'ชื่อตำแหน่งงาน' })
  positionName: string;
  
  @Column({ name: 'RAS_NAMETH', length: 255, comment: 'ระดับตำแหน่ง' })
  positionLevel: string;

  @Column({ name: 'PSN_STARTDATE', type: 'datetime', comment: 'วันที่เริ่มเข้าปฏิบัติงาน' })
  startDate: Date;

  @Column({ name: 'PSN_RETIRE', type: 'enum', enum: ['Y', 'N'], default: 'N', comment: 'สถานะทำงาน(N = ยังทำงาน Y = ไม่ได้ทำงาน)'})
  workStatus: 'Y' | 'N';

  @Column({ name: 'USEMAIL', length: 255 , comment: 'อีเมลของผู้ใช้' })
  email: string;

  @Column({ name: 'PSN_USERNAME', length: 20, comment: 'ชื่อผู้ใช้งาน' })
  username: string;

  @Column({ name: 'USR_PASSWORD', comment: 'รหัสผ่านของผู้ใช้' })
  @Exclude() 
  password: string;

  // -----------------------------------------

  @OneToMany(() => Program, (program) => program.creator)
  programs: Program[];

  @OneToMany(() => Assessment, (assessment) => assessment.creator)
  assessments: Assessment[];

  @OneToMany(() => Submission, (submission) => submission.user)
  submissions: Submission[];

  @OneToMany(() => UserSkill, (us) => us.user, {
    cascade: true,
  })
  userSkills: UserSkill[];

  @OneToMany(() => PermAuditSkill, (pa) => pa.user)
  permAuditSkills: PermAuditSkill[];

  @OneToMany(() => Skill, (s) => s.evaluator)
  evaluatedSkills: Skill[];

  @OneToMany(() => CareerRecords, (cr) => cr.user)
  careerRecords: CareerRecords[];

  @OneToMany(() => FacDepUserRoles, (fdr) => fdr.user)
  facDepUserRoles: FacDepUserRoles[];

  @OneToMany(() => TypePlan, (typePlan) => typePlan.privatePlanUser, {
    nullable: true,
  })
  privatePlans: TypePlan[];
}
