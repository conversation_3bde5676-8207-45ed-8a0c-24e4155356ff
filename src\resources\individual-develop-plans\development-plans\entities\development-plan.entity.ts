import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  Join<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';
import { AgeWorkCriteria } from '../../age-work/entities/age-work-criteria.entity';
import { Faculty } from 'src/resources/faculties/entities/faculty.entity';

@Entity('IDP_T_DEVELOPMENT_PLANS',{ comment: 'แผนพัฒนาบุคลากร (Development Plan)' })
export class DevelopmentPlan {
  @PrimaryGeneratedColumn({ name: 'DP_ID', comment: 'รหัสแผนพัฒนาบุคลากร' })
  id: number;

  @Column({ name: 'DP_NAME', comment: 'ชื่อแผนพัฒนาบุคลากร' })
  name: string;

  @Column({ name: 'DP_DESCRIPTION', comment: 'รายละเอียดแผนพัฒนาบุคลากร' })
  description: string;

  @Column({ name: 'DP_IS_ACTIVE', default: false, comment: 'สถานะการใช้งาน' })
  isActive: boolean;

  @Column({ name: 'DP_PARENT_ID', nullable: true, comment: 'รหัสแผนหลัก' })
  parentId: number;

  @Column({ name: 'FAC_ID', nullable: true, comment: 'รหัสคณะ', length: 10 })
  facId: string;

  @Column({ name: 'AGE_WORK_CRITERIA_ID', nullable: true, comment: 'รหัสเกณฑ์อายุงาน' })
  ageWorkCriteriaId: number;

  @CreateDateColumn({
    name: 'DP_CREATED_AT',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'วันที่สร้างแผนพัฒนาบุคลากร',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'DP_UPDATED_AT',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: 'วันที่ปรับปรุงแผนพัฒนาบุคลากร',
  })
  updatedAt: Date;

  // --- Self-referencing fields ---

  // Many-to-One relationship to itself (parent DevelopmentPlan)
  // A child DevelopmentPlan has one parent (or none, if it's a top-level plan)
  @ManyToOne(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.children,
    {
      nullable: true, // A top-level plan has no parent
      onDelete: 'SET NULL', // If a parent is deleted, set children's parentId to NULL
    },
  )
  @JoinColumn({ name: 'DP_PARENT_ID' }) // This creates the 'DP_PARENT_ID' foreign key column
  parent: DevelopmentPlan;

  // One-to-Many relationship to itself (child DevelopmentPlans)
  // A parent DevelopmentPlan can have many children
  @OneToMany(() => DevelopmentPlan, (developmentPlan) => developmentPlan.parent)
  children: DevelopmentPlan[];

  // ageWork criteria
  @ManyToOne(
    () => AgeWorkCriteria,
    (ageWorkCriteria) => ageWorkCriteria.developmentPlans,
  )
  @JoinColumn({ name: 'AGE_WORK_CRITERIA_ID' })
  ageWorkCriteria: AgeWorkCriteria;

  @OneToMany(() => TypePlan, (typePlan) => typePlan.developmentPlan, {
    eager: true,
    cascade: true,
  })
  typePlans: TypePlan[];

  @ManyToOne(() => Faculty, (f) => f.developmentPlans)
  @JoinColumn({ name: 'FAC_ID' })
  faculty: Faculty;
}
