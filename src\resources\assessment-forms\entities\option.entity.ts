import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('ASM_T_OPTIONS', {
  comment: 'ตารางตัวเลือกในแบบประเมินผล (Quiz / Evaluate Form)',
})
export class Option {
  @PrimaryGeneratedColumn({ name: 'OPT_ID', comment: 'รหัสตัวเลือก' })
  id: number;

  @Column({ name: 'IBL_ID', comment: 'รหัสบล็อกของตัวเลือก' })
  itemBlockId: number;

  @Column({ name: 'OPT_OPTION_TEXT', type: 'text', comment: 'ข้อความตัวเลือก' })
  optionText: string;

  @Column({ name: 'OPT_IMAGE_PATH', type: 'text', nullable: true, comment: 'ที่อยู่รูปภาพประกอบตัวเลือก' })
  imagePath: string | null;

  @Column({ name: 'OPT_VALUE', default: 1, comment: 'ค่าคะแนนตัวเลือก' })
  value: number;

  @Column({ name: 'OPT_SEQUENCE', comment: 'ลำดับตัวเลือก' })
  sequence: number;

  @Column({ name: 'OPT_NEXT_SECTION', nullable: true, default: null, comment: 'ไปยัง section ถัดไป' })
  nextSection: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.options, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.selectedOption)
  responses: Response[];
}
