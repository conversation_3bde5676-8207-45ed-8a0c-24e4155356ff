import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsNumber, IsString } from 'class-validator';

export class PersonalPlansQueryDto {
  @ApiPropertyOptional({
    description: 'Page number',
    example: 1,
    type: 'number',
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    type: 'number',
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search term',
    example: 'john',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Sort by field',
    example: 'id',
    type: 'string',
    default: 'id',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'id';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'ASC',
    enum: ['ASC', 'DESC'],
    default: 'ASC',
  })
  @IsOptional()
  @IsString()
  order?: 'ASC' | 'DESC' = 'ASC';

  @ApiPropertyOptional({
    description: 'Filter by career name',
    example: 'วิศวกร',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  careerFilter?: string;

  @ApiPropertyOptional({
    description: 'Filter by faculty ID',
    example: 1,
    type: 'number',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  facultyFilter?: number;

  @ApiPropertyOptional({
    description: 'Filter by role ID',
    example: 1,
    type: 'number',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  roleFilter?: number;

  @ApiPropertyOptional({
    description: 'Filter by career type',
    example: 'วิชาการ',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  careerTypeFilter?: string;
}
