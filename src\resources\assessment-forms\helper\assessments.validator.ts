import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Assessment } from 'src/resources/assessment-forms/entities/assessment.entity';
import { Repository } from 'typeorm';
import { CreateAssessmentDto } from '../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/updates/update-assessment.dto';

@Injectable()
export class AssessmentValidator {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
  ) {}

  async validateAssessment(id: number): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });
    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }
    return assessment;
  }

  validateCreateDto(dto: CreateAssessmentDto): void {
    const { creatorUserId, programId, type } = dto;
    if (!creatorUserId || !programId || !type) {
      throw new BadRequestException(
        'creatorUserId, programId, and type are required',
      );
    }
  }

  validateUpdateDto(updateDto: UpdateAssessmentDto): void {
    if (!updateDto || Object.keys(updateDto).length === 0) {
      throw new BadRequestException('No update values provided');
    }
  }

  async validateMultipleAssessments(ids: number[]): Promise<Assessment[]> {
    if (!ids?.length) {
      throw new BadRequestException('Assessment IDs are required');
    }

    const assessments = await this.assessmentRepository.findByIds(ids);

    if (assessments.length !== ids.length) {
      const foundIds = assessments.map((a) => a.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Assessments not found: ${missingIds.join(', ')}`,
      );
    }

    return assessments;
  }

  async validateAssessmentsForDuplication(
    sourceId: number,
    targetId: number,
  ): Promise<{
    sourceAssessment: Assessment;
    targetAssessment: Assessment;
  }> {
    const [sourceAssessment, targetAssessment] = await Promise.all([
      this.validateAssessment(sourceId),
      this.validateAssessment(targetId),
    ]);

    return { sourceAssessment, targetAssessment };
  }

  validateDuplicationData(sourceAssessment: Assessment): void {
    if (!sourceAssessment?.itemBlocks?.length) {
      throw new BadRequestException(
        'Source assessment has no content to duplicate',
      );
    }
  }

  validateBoolean(value: any): boolean {
    return typeof value === 'string'
      ? value.toLowerCase() === 'true'
      : Boolean(value);
  }

  validateFieldUpdate(key: string, value: any, entity: any): any {
    if (value === undefined || !(key in entity)) {
      return undefined;
    }

    const booleanFields = new Set(['status', 'isPrototype', 'responseEdit']);
    return booleanFields.has(key) ? this.validateBoolean(value) : value;
  }
}
