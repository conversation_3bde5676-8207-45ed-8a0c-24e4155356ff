import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>umn,
  OneToOne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('ASM_T_HEADER_BODIES', {
  comment: 'ตารางบล็อคส่วนหัวในเนื้อหาของแบบประเมินผล (Quiz / Evaluate Form)',
})
export class HeaderBody {
  @PrimaryGeneratedColumn({ name: 'HDB_ID', comment: 'รหัสส่วนหัว' })
  id: number;

  @Column({ name: 'HDB_TITLE', comment: 'หัวข้อ' })
  title: string;

  @Column({ name: 'HDB_DESCRIPTION', nullable: true, comment: 'รายละเอียดหัวข้อ' })
  description: string | null;

  @Column({ name: 'IBL_ID', comment: 'รหัสบล็อก' })
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.headerBody, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;
}
