import { IsNotEmpty, IsString, IsInt, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAgeWorkDto {
  @ApiProperty({
    description: 'Name of the age work range',
    example: 'อายุงาน 0-2 ปี',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Starting year of work experience',
    example: 0,
  })
  @IsNotEmpty()
  @IsInt()
  startYear: number;

  @ApiProperty({
    description: 'Ending year of work experience',
    example: 2,
  })
  @IsNotEmpty()
  @IsInt()
  endYear: number;

  @ApiProperty({
    description: 'ID of the age work criteria plan this belongs to',
    example: 1,
  })
  @IsNotEmpty()
  @IsInt()
  ageWorkCriteriaId: number;
}

export class UpdateAgeWorkDto {
  @ApiProperty({
    description: 'Name of the age work range',
    example: 'อายุงาน 0-3 ปี',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Starting year of work experience',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsInt()
  startYear?: number;

  @ApiProperty({
    description: 'Ending year of work experience',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsInt()
  endYear?: number;

  @ApiProperty({
    description: 'ID of the age work criteria plan this belongs to',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  ageWorkCriteriaId?: number;
}
