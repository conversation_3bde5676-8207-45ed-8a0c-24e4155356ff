import { DataSource } from 'typeorm';
import { Faculty } from '../../resources/faculties/entities/faculty.entity';
import { Campus } from '../../resources/campus/entities/campus.entity';

export async function seedFaculties(dataSource: DataSource) {
  const facultyRepository = dataSource.getRepository(Faculty);
  const campusRepository = dataSource.getRepository(Campus);

  console.log('Starting faculties seeding...');

  // Get campus data first
  const campuses = await campusRepository.find();
  if (campuses.length === 0) {
    console.log('No campus data found. Please seed campus data first.');
    return;
  }

  const facultiesData = [
    // คณะในวิทยาเขตบางแสน (campus id: 1)
    { id: '01', name: 'คณะดนตรีและการแสดง', campusId: '1' },
    { id: '02', name: 'คณะแพทยศาสตร์', campusId: '1' },
    { id: '03', name: 'คณะโลจิสติกส์', campusId: '1' },
    { id: '04', name: 'คณะวิทยาศาสตร์การกีฬา', campusId: '1' },
    { id: '05', name: 'คณะศึกษาศาสตร์', campusId: '1' },
    { id: '06', name: 'วิทยาลัยนานาชาติ', campusId: '1' },
    { id: '07', name: 'คณะเภสัชศาสตร์', campusId: '1' },
    { id: '08', name: 'คณะมนุษยศาสตร์และสังคมศาสตร์', campusId: '1' },
    { id: '09', name: 'คณะวิทยาการสารสนเทศ', campusId: '1' },
    { id: '10', name: 'คณะวิศวกรรมศาสตร์', campusId: '1' },
    { id: '11', name: 'คณะสาธารณสุขศาสตร์', campusId: '1' },
    { id: '12', name: 'คณะพยาบาลศาสตร์', campusId: '1' },
    { id: '13', name: 'คณะรัฐศาสตร์และนิติศาสตร์', campusId: '1' },
    { id: '14', name: 'คณะวิทยาศาสตร์', campusId: '1' },
    { id: '15', name: 'คณะศิลปกรรมศาสตร์', campusId: '1' },
    { id: '16', name: 'คณะบริหารธุรกิจ', campusId: '1' },
    { id: '17', name: 'คณะสหเวชศาสตร์', campusId: '1' },
    { id: '18', name: 'บัณฑิตวิทยาลัย', campusId: '1' },
    { id: '19', name: 'สำนักงานสภามหาวิทยาลัย', campusId: '1' },
    { id: '20', name: 'สำนักงานอธิการบดี', campusId: '1' },
    { id: '21', name: 'สำนักคอมพิวเตอร์', campusId: '1' },
    { id: '22', name: 'สำนักบริการวิชาการ', campusId: '1' },
    { id: '23', name: 'สำนักหอสมุด', campusId: '1' },
    { id: '24', name: 'สถาบันวิทยาศาสตร์ทางทะเล', campusId: '1' },
    { id: '25', name: 'สถาบันภาษา', campusId: '1' },
    { id: '26', name: 'ศูนย์ภูมิภาคเทคโนโลยีอวกาศและภูมิสารสนเทศ ภาคตะวันออก', campusId: '1' },
    { id: '27', name: 'ศูนย์จีนศึกษา', campusId: '1' },
    { id: '28', name: 'โรงเรียนสาธิต', campusId: '1' },
    
    // คณะในวิทยาเขตจันทบุรี (campus id: 2)
    { id: '29', name: 'คณะเทคโนโลยีทางทะเล', campusId: '2' },
    { id: '30', name: 'คณะวิทยาศาสตร์และศิลปศาสตร์', campusId: '2' },
    { id: '31', name: 'คณะอัญมณี', campusId: '2' },
    
    // คณะในวิทยาเขตสระแก้ว (campus id: 3)
    { id: '32', name: 'คณะเทคโนโลยีการเกษตร', campusId: '3' },
    { id: '33', name: 'คณะวิทยาศาสตร์และสังคมศาสตร์', campusId: '3' },
  ];

  for (const facultyData of facultiesData) {
    const existingFaculty = await facultyRepository.findOne({
      where: { id: facultyData.id }
    });

    if (!existingFaculty) {
      // Find the campus
      const campus = await campusRepository.findOne({
        where: { id: facultyData.campusId }
      });

      if (!campus) {
        console.log(`Campus with id ${facultyData.campusId} not found for faculty ${facultyData.name}`);
        continue;
      }

      const faculty = facultyRepository.create({
        id: facultyData.id,
        name: facultyData.name,
        campus: campus
      });
      
      await facultyRepository.save(faculty);
      console.log(`Created faculty: ${facultyData.name} (${facultyData.id}) in ${campus.name}`);
    } else {
      console.log(`Faculty already exists: ${facultyData.name} (${facultyData.id})`);
    }
  }

  console.log('Faculties seeding completed!');
}
