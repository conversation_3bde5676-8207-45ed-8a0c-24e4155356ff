import { Controller, Get, Param, Post, Body, Patch, Delete, Query } from '@nestjs/common';
import { PositionService } from './positions.service';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { ApiTags, ApiOperation, ApiParam, ApiBearerAuth, ApiBody, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { DataParams } from 'src/types';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';

@ApiBearerAuth()
@ApiTags('Positions')
@Controller('positions')
export class PositionController {
  constructor(private readonly positionService: PositionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new position' })
  @ApiBody({ type: CreatePositionDto })
  create(@Body() createPositionDto: CreatePositionDto) {
    return this.positionService.create(createPositionDto);
  }

  @Get()
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all positions' })
  @ApiResponse({ status: 200, description: 'Return all positions' })
  findAll(@Query() query: DataParams) {
    return this.positionService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a position by ID' })
  @ApiParam({ name: 'id', description: 'Position ID' })
  findOne(@Param('id') id: string) {
    return this.positionService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a position' })
  @ApiParam({ name: 'id', description: 'Position ID' })
  @ApiBody({ type: UpdatePositionDto })
  update(@Param('id') id: string, @Body() updatePositionDto: UpdatePositionDto) {
    return this.positionService.update(+id, updatePositionDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a position' })
  @ApiParam({ name: 'id', description: 'Position ID' })
  remove(@Param('id') id: string) {
    return this.positionService.remove(+id);
  }
}
