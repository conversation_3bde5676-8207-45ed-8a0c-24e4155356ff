import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from './entities/role.entity';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
import { PermissionsModule } from '../permissions/permissions.module';
import { User } from '../users/entities/user.entity';
import { Faculty } from '../faculties/entities/faculty.entity';
import { FacDepUserRoles } from '../faculties/entities/faculty-deparment-user-role.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Role, User, Faculty, FacDepUserRoles]),
    PermissionsModule,
  ],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
