import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateProgramDto {
  @ApiProperty({
    description: 'ชื่อโครงงาน',
    example: 'โครงการพัฒนาบุคลากร',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'รายละเอียดโครงงาน',
    example: 'โครงการเพื่อพัฒนาทักษะและความรู้ของบุคลากร',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'รหัสผู้สร้างโครงงาน',
    example: 1,
    type: Number,
  })
  @IsString()
  @IsNotEmpty()
  creatorId: string;
}