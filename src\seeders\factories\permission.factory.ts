import { Faker } from '@faker-js/faker';
import { setSeederFactory } from 'typeorm-extension';
import { Permission } from '../../resources/permissions/entities/permission.entity';

export const PermissionFactory = setSeederFactory(Permission, (faker) => {
  const permission = new Permission();
  permission.name = faker.helpers.slugify(faker.lorem.words(2)).toLowerCase();
  permission.descEn = faker.lorem.words(3);
  permission.descTh = faker.lorem.words(3);
  permission.status = true;
  permission.isDefault = false;
  return permission;
});
