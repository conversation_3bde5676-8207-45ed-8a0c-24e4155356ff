import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { CompetenciesService } from './competencies.service';
import { CreateCompetenciesDto } from './dto/create-competencies.dto';
import { UpdateCompetenciesDto } from './dto/update-competencies.dto';
import { ApiBearerAuth, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { AuthGuard } from 'src/auth/auth.guard';
import { ApiQuery } from '@nestjs/swagger';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsEnum, IsArray, IsInt } from 'class-validator';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { CompetencyType } from './enum/competencies-type.enum';

@ApiBearerAuth()
// @UseGuards(AuthGuard)
@ApiTags('Competencies')
@Controller('Competencies')
export class CompetenciesController {
  constructor(private readonly competenciesService: CompetenciesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a competency' })
  @ApiResponse({ status: 201 })
  async create(@Body() dto: CreateCompetenciesDto) {
    return this.competenciesService.create(dto);
  }

  //   @Get()
  //   @DefaultQueryParams()
  //   findAll(@Query() query: DataParams) {
  //     return this.competenciesService.findAll(query);
  //   }

  @Get()
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all competencies (with pagination)' })
  @ApiResponse({ status: 200 })
  @ApiQuery({
    enum: [
      'สมรรถนะหลัก',
      'สมรรถนะสายวิชาการ',
      'สมรรถนะสายสนับสนุนวิชการ',
      'สมรรถนะทางการบริหาร',
    ],
    required: false,
    name: 'career_type',
  })
  findAll(@Query() query: DataParams & { career_type?: string }) {
    return this.competenciesService.findAll(query, query.career_type);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get competency by ID' })
  @ApiParam({ name: 'id', description: 'Competency ID' })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Competency not found' })
  async findOne(@Param('id') id: string) {
    return this.competenciesService.findOne(+id);
  }

  @Get('type/:career_type')
  @ApiOperation({ summary: 'Get competency by Type' })
  @ApiParam({ name: 'id', description: 'Competency ID' })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Competency not found' })
  async findByType(@Param('career_type') career_type: CompetencyType) {
    console.log('controller');
    return this.competenciesService.findByType(career_type);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a competency' })
  @ApiParam({ name: 'id', description: 'Competency ID' })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Competency not found' })
  async update(@Param('id') id: string, @Body() dto: UpdateCompetenciesDto) {
    return this.competenciesService.update(+id, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a competency' })
  @ApiParam({ name: 'id', description: 'Competency ID' })
  @ApiResponse({ status: 204, description: 'Deleted successfully' })
  async remove(@Param('id') id: string) {
    await this.competenciesService.remove(+id);
  }
}
