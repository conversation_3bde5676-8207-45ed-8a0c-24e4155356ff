import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Skill } from './entities/skill.entity';
import { In, Repository } from 'typeorm';
import { CreateSkillDto } from './dto/create-skill.dto';
import { UpdateSkillDto } from './dto/update-skill.dto';
import { Program } from 'src/resources/programs/entities/program.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { instanceToPlain } from 'class-transformer';
import { Competency } from '../competencies/entities/competencies.entity';
import { CompetencySkill } from './entities/competency-skill.entity';
import type { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

@Injectable()
export class SkillsService {
  constructor(
    @InjectRepository(Skill)
    private readonly skillRepo: Repository<Skill>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Program)
    private readonly programRepo: Repository<Program>,
    @InjectRepository(Competency)
    private readonly competencyRepo: Repository<Competency>,
    @InjectRepository(CompetencySkill)
    private readonly competencySkillRepo: Repository<CompetencySkill>,
  ) {}

  async create(dto: CreateSkillDto): Promise<Skill> {
    const skill = this.skillRepo.create({
      name: dto.name,
      description: dto.description,
      career_type: dto.career_type,
      program: await this.programRepo.findOneBy({ id: dto.programId }),
      tracking: dto.tracking,
    });

    if (dto.evaluatorId) {
      const evaluator = await this.userRepo.findOneBy({ id: dto.evaluatorId });
      if (!evaluator) {
        throw new BadRequestException(
          `Evaluator with ID ${dto.evaluatorId} not found`,
        );
      }
      skill.evaluator = evaluator;
    }

    const savedSkill = await this.skillRepo.save(skill);

    // === บันทึก CompetencySkills ===
    if (dto.competencyIds?.length) {
      const mappings = dto.competencyIds.map((competencyId) =>
        this.competencySkillRepo.create({
          skill: savedSkill,
          competency: { id: competencyId },
        }),
      );
      await this.competencySkillRepo.save(mappings);
    }

    return instanceToPlain(savedSkill) as Skill;
  }

  async findAll(query: PaginationQueryDto & { career_type?: string }) {
    const where: any = {};

    if (query.career_type) {
      where.career_type = query.career_type;
    }
    console.log(where);

    const [data, total] = await this.skillRepo.findAndCount({
      where,
      relations: ['evaluator', 'program'],
      order: { [query.sortBy || 'id']: query.order || 'ASC' },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    });

    return {
      data: instanceToPlain(data),
      total,
      curPage: query.page,
      hasPrev: query.page > 1,
      hasNext: query.page * query.limit < total,
    };
  }

  async findOne(id: number): Promise<Skill & { competencies: Competency[] }> {
    const skill = await this.skillRepo.findOne({
      where: { id },
      relations: ['evaluator', 'program'],
    });

    if (!skill) throw new NotFoundException(`Skill ID ${id} not found`);

    const mappings = await this.competencySkillRepo.find({
      where: { skill: { id } },
      relations: ['competency'],
    });

    const competencies = mappings.map((m) => m.competency);

    return instanceToPlain({
      ...skill,
      competencies,
    }) as Skill & { competencies: Competency[] };
  }

  async update(id: number, dto: UpdateSkillDto): Promise<Skill> {
    const skill = await this.skillRepo.findOne({
      where: { id },
      relations: ['program', 'evaluator'],
    });
    if (!skill) throw new NotFoundException(`Skill ID ${id} not found`);

    // Update evaluator
    if (dto.evaluatorId !== undefined) {
      skill.evaluator = dto.evaluatorId
        ? await this.userRepo.findOneBy({ id: dto.evaluatorId })
        : null;
    }

    // Update program
    if (dto.programId) {
      const program = await this.programRepo.findOneBy({ id: dto.programId });
      if (!program) {
        throw new BadRequestException(`Program ID ${dto.programId} not found`);
      }
      skill.program = program;
    }

    // Update fields
    Object.assign(skill, dto);

    const updatedSkill = await this.skillRepo.save(skill);

    // === Update CompetencySkill mapping ===
    if (dto.competencyIds) {
      // ลบของเก่า
      await this.competencySkillRepo.delete({ skill: { id } });

      // เพิ่มของใหม่
      const foundCompetencies = await this.competencyRepo.findBy({
        id: In(dto.competencyIds),
      });

      if (foundCompetencies.length !== dto.competencyIds.length) {
        throw new BadRequestException(`Some competencyIds are invalid`);
      }

      const mappings = foundCompetencies.map((competency) =>
        this.competencySkillRepo.create({
          skill: updatedSkill,
          competency,
        }),
      );

      await this.competencySkillRepo.save(mappings);
    }

    return instanceToPlain(updatedSkill) as Skill;
  }

  async remove(id: number): Promise<void> {
    const skill = await this.skillRepo.findOneBy({ id });
    if (!skill) throw new NotFoundException(`Skill ID ${id} not found`);
    await this.skillRepo.remove(skill);
  }
}
