import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Faculty } from 'src/resources/faculties/entities/faculty.entity';

@Entity('HRD_T_DEPARTMENTS', { comment: 'ตารางกลุ่มงาน' })
export class Department {
  @Column({ name: 'DEP_ID', comment: 'รหัสกลุ่มงาน', primary: true, nullable: false, length: 10 })
  id: string;

  @Column({ name: 'DEP_NAMETH', nullable: false, comment: 'ชื่อกลุ่มงาน', length: 255 })
  name: string;

  @ManyToOne(() => Faculty, (faculty) => faculty.departments, { nullable: false })
  @JoinColumn({ name: 'FAC_ID' })
  faculty: Faculty;
}
