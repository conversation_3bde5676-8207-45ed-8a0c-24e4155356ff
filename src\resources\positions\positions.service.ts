import { Injectable } from '@nestjs/common';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { EntityManager, Like } from 'typeorm';
import { Position } from './entities/position.entity';
import { DataParams, createPaginatedResponse } from 'src/types';

@Injectable()
export class PositionService {
  constructor(private readonly entityManager: EntityManager) {}

  async findAll(pag?: DataParams) {
    const repoPosition = this.entityManager.getRepository(Position);
    
    const [positions, total] = await repoPosition.findAndCount({
      where: {
        name: pag?.search ? Like(`%${pag.search}%`) : undefined,
      },
      order: {
        [pag?.sortBy || 'id']: pag?.order || 'ASC',
      },
      skip: (pag?.page - 1) * pag?.limit,
      take: pag?.limit,
    });

    return createPaginatedResponse(positions, total, pag?.page || 1, pag?.limit || 10);
  }

  findOne(id: number) {
    const repoPosition = this.entityManager.getRepository(Position);
    return repoPosition.findOneOrFail({ where: { id } });
  }

  create(createPositionDto: CreatePositionDto) {
    const repoPosition = this.entityManager.getRepository(Position);
    const position = repoPosition.create(createPositionDto);
    return repoPosition.save(position);
  }

  update(id: number, updatePositionDto: UpdatePositionDto) {
    const repoPosition = this.entityManager.getRepository(Position);
    return repoPosition.update(id, updatePositionDto);
  }

  remove(id: number) {
    const repoPosition = this.entityManager.getRepository(Position);
    return repoPosition.delete(id);
  }
}
