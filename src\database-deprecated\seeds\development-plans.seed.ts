import { DataSource } from 'typeorm';
import { DevelopmentPlan } from '../../resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import { Faculty } from '../../resources/faculties/entities/faculty.entity';

export async function seedDevelopmentPlans(dataSource: DataSource) {
  const developmentPlanRepository = dataSource.getRepository(DevelopmentPlan);
  const facultyRepository = dataSource.getRepository(Faculty);

  console.log('Starting development plans seeding...');

  // Get some faculty data first
  const faculties = await facultyRepository.find({ take: 5 });

  const developmentPlansData = [
    {
      name: 'แผนพัฒนาบุคลากรประจำปี 2567',
      description: 'แผนพัฒนาบุคลากรแบบครอบคลุมสำหรับการประเมินผลการปฏิบัติงานประจำปี 2567',
      isActive: true,
      parentId: null,
      faculty: faculties.length > 0 ? faculties[0] : null,
      ageWorkCriteriaId: null,
    },
    {
      name: 'โปรแกรมพัฒนาผู้นำไตรมาส 3',
      description: 'การพัฒนาเป้าหมายสำหรับผู้นำใหม่ในไตรมาส 3',
      isActive: true,
      parentId: null,
      faculty: faculties.length > 1 ? faculties[1] : null,
      ageWorkCriteriaId: null,
    },
    {
      name: 'การเสริมสร้างทักษะเทคนิค - Backend',
      description: 'แผนเพื่อเสริมสร้างทักษะการพัฒนา backend สำหรับทีมวิศวกรรม',
      isActive: true,
      parentId: null,
      faculty: faculties.length > 2 ? faculties[2] : null,
      ageWorkCriteriaId: null,
    },
    {
      name: 'การฝึกอบรมความเป็นเลิศด้านการบริการลูกค้า',
      description: 'โปรแกรมการฝึกอบรมเพื่อปรับปรุงการโต้ตอบการบริการลูกค้า',
      isActive: false,
      parentId: null,
      faculty: faculties.length > 3 ? faculties[3] : null,
      ageWorkCriteriaId: null,
    },
  ];

  for (const planData of developmentPlansData) {
    const existingPlan = await developmentPlanRepository.findOne({
      where: { name: planData.name }
    });

    if (!existingPlan) {
      const developmentPlan = developmentPlanRepository.create(planData);
      await developmentPlanRepository.save(developmentPlan);
      console.log(`Created development plan: ${planData.name}`);
    } else {
      console.log(`Development plan already exists: ${planData.name}`);
    }
  }

  console.log('Development plans seeding completed!');
}