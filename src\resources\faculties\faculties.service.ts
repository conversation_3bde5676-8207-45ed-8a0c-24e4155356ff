import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateFacultyDto } from './dto/create-faculty.dto';
import { UpdateFacultyDto } from './dto/update-faculty.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Faculty } from './entities/faculty.entity';
import { FacDepUserRoles } from './entities/faculty-deparment-user-role.entity';
import { Repository, Like, In } from 'typeorm';
import { DataParams, DataResponse, createPaginatedResponse } from 'src/types';
import { FacultyRoleItem } from './interfaces/faculty-role.interface';
import { Role } from 'src/resources/roles/entities/role.entity';
import { FacultyUserItem } from 'src/resources/users/users.service';
import { User } from '../users/entities/user.entity';

export interface FacultyListItem {
  id: string;
  name: string;
  roles: Role[];
}

export interface UserByFaculty {
  user: {
    id: string;
    roleId: number;
    lastNameTh: string;
    firstNameTh: string;
    roles?: Role[];
  };
  facultyId: number;
  facultyName: string;
}

@Injectable()
export class FacultiesService {
  constructor(
    @InjectRepository(Faculty)
    private readonly facultyRepository: Repository<Faculty>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(FacDepUserRoles)
    private readonly facultyUserRolesRepository: Repository<FacDepUserRoles>,

  ) {}

  create(createFacultyDto: CreateFacultyDto) {
    return this.facultyRepository.save(createFacultyDto);
  }

  async findAll(pag: DataParams): Promise<DataResponse<FacultyListItem>> {
    console.log('Service faculties findAll');
    
    // Get faculties with basic info first
    const queryBuilder = this.facultyRepository.createQueryBuilder('faculty');
    
    if (pag.search) {
      queryBuilder.where('faculty.name LIKE :search', { search: `%${pag.search}%` });
    }

    queryBuilder
      .orderBy('faculty.id', pag.order || 'ASC')
      .skip((pag.page - 1) * pag.limit)
      .take(pag.limit);

    const [faculties, total] = await queryBuilder.getManyAndCount();

    // For each faculty, get roles through departments
    const facultiesWithRoles = await Promise.all(
      faculties.map(async (faculty) => {
        const rolesQuery = this.facultyUserRolesRepository
          .createQueryBuilder('facDepUserRoles')
          .leftJoinAndSelect('facDepUserRoles.role', 'role')
          .leftJoin('facDepUserRoles.department', 'department')
          .leftJoin('department.faculty', 'faculty')
          .where('faculty.id = :facultyId', { facultyId: faculty.id })
          .select(['role.id', 'role.name', 'role.description']);

        const roleResults = await rolesQuery.getRawMany();
        
        // Remove duplicates
        const roleMap = new Map();
        roleResults.forEach((result) => {
          if (result.role_id && !roleMap.has(result.role_id)) {
            roleMap.set(result.role_id, {
              id: result.role_id,
              name: result.role_name,
              description: result.role_description,
            });
          }
        });

        return {
          id: faculty.id,
          name: faculty.name,
          roles: Array.from(roleMap.values()),
        };
      })
    );

    return createPaginatedResponse(
      facultiesWithRoles,
      total,
      pag.page,
      pag.limit,
    );
  }

  findOne(id: string) {
    const faculty = this.facultyRepository.findOne({ where: { id } });
    if (!faculty) {
      throw new Error(`Faculty with id ${id} not found`);
    }
    return faculty;
  }

  update(id: string, updateFacultyDto: UpdateFacultyDto) {
    return this.facultyRepository.update(id, updateFacultyDto);
  }

  remove(id: string) {
    return this.facultyRepository.delete(id);
  }

  async getFacultiesRoles(
    pag: DataParams,
    facultyId?: string,
    roleId?: number,
  ): Promise<DataResponse<FacultyRoleItem>> {
    try {
      // Build the query using FacDepUserRoles as the starting point
      const queryBuilder = this.facultyUserRolesRepository.createQueryBuilder('facDepUserRoles');

      // Join with related entities
      queryBuilder
        .leftJoinAndSelect('facDepUserRoles.user', 'user')
        .leftJoinAndSelect('facDepUserRoles.role', 'role')
        .leftJoinAndSelect('facDepUserRoles.department', 'department')
        .leftJoinAndSelect('department.faculty', 'faculty');

      // Apply filters
      if (facultyId) {
        queryBuilder.andWhere('faculty.id = :facultyId', { facultyId });
      }

      if (roleId) {
        queryBuilder.andWhere('role.id = :roleId', { roleId });
      }

      // Apply search if provided
      if (pag.search) {
        queryBuilder.andWhere(
          '(faculty.name LIKE :search OR user.firstNameTh LIKE :search OR user.lastNameTh LIKE :search OR user.email LIKE :search)',
          { search: `%${pag.search}%` },
        );
      }

      // Apply sorting
      const sortField = pag.sortBy || 'faculty.id';
      const sortOrder = pag.order || 'ASC';
      queryBuilder.orderBy(sortField, sortOrder);

      // Apply pagination
      queryBuilder.skip((pag.page - 1) * pag.limit).take(pag.limit);

      // Execute the query
      const [facDepUserRoles, total] = await queryBuilder.getManyAndCount();

      // Group by faculty
      const facultyMap = new Map();

      facDepUserRoles.forEach((facDepUserRole) => {
        const faculty = facDepUserRole.department?.faculty;
        if (!faculty) return;

        const facultyId = faculty.id;
        
        if (!facultyMap.has(facultyId)) {
          facultyMap.set(facultyId, {
            facultyId: faculty.id,
            facultyName: faculty.name,
            users: new Map(),
          });
        }

        const facultyData = facultyMap.get(facultyId);
        const user = facDepUserRole.user;
        const userId = user.id;

        if (!facultyData.users.has(userId)) {
          facultyData.users.set(userId, {
            id: user.id,
            name: `${user.firstNameTh} ${user.lastNameTh}`,
            email: user.email,
            firstName: user.firstNameTh,
            lastName: user.lastNameTh,
            startDate: user.startDate,
            roles: [],
          });
        }

        const userData = facultyData.users.get(userId);
        const role = facDepUserRole.role;

        // Check if role already exists to avoid duplicates
        const roleExists = userData.roles.some((r) => r.id === role.id);
        if (!roleExists) {
          userData.roles.push({
            id: role.id,
            name: role.name,
            description: role.description,
          });
        }
      });

      // Convert maps to arrays
      const data: FacultyRoleItem[] = Array.from(facultyMap.values()).map((facultyData) => ({
        facultyId: facultyData.facultyId,
        facultyName: facultyData.facultyName,
        users: Array.from(facultyData.users.values()),
      }));

      return createPaginatedResponse(data, total, pag.page, pag.limit);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve faculties with roles: ${error.message}`,
      );
    }
  }

  async getUsersFaculty(
    facultyId: string,
    pag: DataParams,
  ): Promise<DataResponse<FacultyUserItem>> {
    try {
      // Find the faculty first to get faculty information
      const faculty = await this.facultyRepository.findOne({
        where: { id: facultyId },
      });

      if (!faculty) {
        throw new BadRequestException(`Faculty with id ${facultyId} not found`);
      }

      // Build query using FacDepUserRoles and join through department to faculty
      const queryBuilder = this.facultyUserRolesRepository.createQueryBuilder('facDepUserRoles');

      // Join with related entities
      queryBuilder
        .leftJoinAndSelect('facDepUserRoles.user', 'user')
        .leftJoinAndSelect('facDepUserRoles.role', 'role')
        .leftJoinAndSelect('facDepUserRoles.department', 'department')
        .leftJoin('department.faculty', 'faculty')
        .where('faculty.id = :facultyId', { facultyId });

      // Apply search if provided 
      if (pag.search) {
        queryBuilder.andWhere(
          '(user.firstNameTh LIKE :search OR user.lastNameTh LIKE :search OR user.email LIKE :search)',
          { search: `%${pag.search}%` },
        );
      }

      // Apply sorting
      const sortField = pag.sortBy || 'user.id';
      const sortOrder = pag.order || 'ASC';
      queryBuilder.orderBy(sortField, sortOrder);

      // Apply pagination
      queryBuilder.skip((pag.page - 1) * pag.limit).take(pag.limit);

      // Execute the query
      const [facDepUserRoles, total] = await queryBuilder.getManyAndCount();

      // Group by user to avoid duplicates and collect roles
      const userMap = new Map();

      facDepUserRoles.forEach((facDepUserRole) => {
        const user = facDepUserRole.user;
        const userId = user.id;

        if (!userMap.has(userId)) {
          userMap.set(userId, {
            id: user.id,
            email: user.email,
            firstName: user.firstNameTh,
            lastName: user.lastNameTh,
            facultyId: faculty.id,
            facultyName: faculty.name,
            roles: [],
          });
        }

        const userData = userMap.get(userId);
        const role = facDepUserRole.role;

        // Check if role already exists to avoid duplicates
        const roleExists = userData.roles.some((r) => r.id === role.id);
        if (!roleExists) {
          userData.roles.push({
            id: role.id,
            name: role.name,
            description: role.description,
          });
        }
      });

      // Convert map to array
      const data: FacultyUserItem[] = Array.from(userMap.values());

      return {
        data,
        total,
        currentPage: pag.page,
        itemsPerPage: pag.limit,
        totalPages: Math.ceil(total / pag.limit),
        hasNext: total > pag.page * pag.limit,
        hasPrev: pag.page > 1,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve users for faculty ${facultyId}: ${error.message}`,
      );
    }
  }
}
